import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Message = Tables<'messages'>
type MessageInsert = TablesInsert<'messages'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { data: messages, error } = await supabase
        .from('messages')
        .select(`
          *,
          patients(name)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      // Transform the data to include patient_name for compatibility
      const transformedMessages = messages?.map(msg => ({
        ...msg,
        patient_name: msg.patients?.name
      })) || []

      return createApiResponse(transformedMessages)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return with<PERSON>uth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const messageData: MessageInsert = {
        ...body,
        user_id: userId,
        channel: body.channel || 'whatsapp',
        status: body.status || 'sent'
      }

      const { data: message, error } = await supabase
        .from('messages')
        .insert(messageData)
        .select(`
          *,
          patients(name)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      // Transform the data to include patient_name for compatibility
      const transformedMessage = {
        ...message,
        patient_name: message.patients?.name
      }

      return createApiResponse(transformedMessage, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
