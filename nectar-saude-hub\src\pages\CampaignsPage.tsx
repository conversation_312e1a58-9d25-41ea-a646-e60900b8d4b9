import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Megaphone, Plus, Play, Pause, Edit, Trash2, Users, Send, Eye } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useSupabase } from '@/hooks/useSupabase';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

type Campaign = {
  id: string;
  name: string;
  description?: string;
  message_content: string;
  channel: 'whatsapp' | 'email' | 'sms';
  status: 'draft' | 'active' | 'paused' | 'completed';
  scheduled_at?: string;
  sent_count: number;
  delivered_count: number;
  read_count: number;
  created_at: string;
};

const CampaignsPage = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null);
  const { user } = useSupabase();
  const { toast } = useToast();

  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    message_content: '',
    channel: 'whatsapp' as const,
    scheduled_at: ''
  });

  useEffect(() => {
    if (user) {
      fetchCampaigns();
    }
  }, [user]);

  const fetchCampaigns = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('campaigns')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      toast({
        title: "Erro ao carregar campanhas",
        description: error.message,
        variant: "destructive"
      });
    } else {
      setCampaigns(data || []);
    }
    setLoading(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    const campaignData = {
      ...campaignForm,
      user_id: user.id,
      scheduled_at: campaignForm.scheduled_at || null
    };

    let error;
    
    if (editingCampaign) {
      const { error: updateError } = await supabase
        .from('campaigns')
        .update(campaignData)
        .eq('id', editingCampaign.id);
      error = updateError;
    } else {
      const { error: insertError } = await supabase
        .from('campaigns')
        .insert(campaignData);
      error = insertError;
    }

    if (error) {
      toast({
        title: editingCampaign ? "Erro ao atualizar campanha" : "Erro ao criar campanha",
        description: error.message,
        variant: "destructive"
      });
    } else {
      toast({
        title: editingCampaign ? "Campanha atualizada" : "Campanha criada",
        description: editingCampaign ? "A campanha foi atualizada com sucesso" : "A campanha foi criada com sucesso"
      });
      handleCloseDialog();
      fetchCampaigns();
    }
  };

  const handleEdit = (campaign: Campaign) => {
    setEditingCampaign(campaign);
    setCampaignForm({
      name: campaign.name,
      description: campaign.description || '',
      message_content: campaign.message_content,
      channel: campaign.channel,
      scheduled_at: campaign.scheduled_at ? new Date(campaign.scheduled_at).toISOString().slice(0, 16) : ''
    });
    setDialogOpen(true);
  };

  const handleDelete = async (campaignId: string) => {
    if (!confirm('Tem certeza que deseja excluir esta campanha?')) return;

    const { error } = await supabase
      .from('campaigns')
      .delete()
      .eq('id', campaignId);

    if (error) {
      toast({
        title: "Erro ao excluir campanha",
        description: error.message,
        variant: "destructive"
      });
    } else {
      toast({
        title: "Campanha excluída",
        description: "A campanha foi removida com sucesso"
      });
      fetchCampaigns();
    }
  };

  const updateCampaignStatus = async (campaignId: string, status: string) => {
    const { error } = await supabase
      .from('campaigns')
      .update({ status })
      .eq('id', campaignId);

    if (error) {
      toast({
        title: "Erro ao atualizar campanha",
        description: error.message,
        variant: "destructive"
      });
    } else {
      toast({
        title: "Status atualizado",
        description: `Campanha ${status === 'active' ? 'ativada' : 'pausada'} com sucesso`
      });
      fetchCampaigns();
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingCampaign(null);
    setCampaignForm({
      name: '',
      description: '',
      message_content: '',
      channel: 'whatsapp',
      scheduled_at: ''
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-success text-success-foreground';
      case 'completed': return 'bg-primary text-primary-foreground';  
      case 'paused': return 'bg-warning text-warning-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp': return '📱';
      case 'email': return '📧';
      case 'sms': return '💬';
      default: return '📢';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Campanhas</h1>
          <p className="text-muted-foreground">Gerencie campanhas de marketing e comunicação</p>
        </div>
        
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setEditingCampaign(null)}>
              <Plus className="mr-2 h-4 w-4" />
              Nova Campanha
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>
                  {editingCampaign ? 'Editar Campanha' : 'Criar Nova Campanha'}
                </DialogTitle>
                <DialogDescription>
                  Configure sua campanha de comunicação
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome da campanha *</Label>
                  <Input
                    id="name"
                    value={campaignForm.name}
                    onChange={(e) => setCampaignForm({ ...campaignForm, name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Descrição</Label>
                  <Input
                    id="description"
                    value={campaignForm.description}
                    onChange={(e) => setCampaignForm({ ...campaignForm, description: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="channel">Canal</Label>
                  <Select
                    value={campaignForm.channel}
                    onValueChange={(value: any) => setCampaignForm({ ...campaignForm, channel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="whatsapp">📱 WhatsApp</SelectItem>
                      <SelectItem value="email">📧 Email</SelectItem>
                      <SelectItem value="sms">💬 SMS</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message_content">Mensagem *</Label>
                  <Textarea
                    id="message_content"
                    value={campaignForm.message_content}
                    onChange={(e) => setCampaignForm({ ...campaignForm, message_content: e.target.value })}
                    required
                    rows={4}
                    placeholder="Digite o conteúdo da sua mensagem..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="scheduled_at">Agendamento (opcional)</Label>
                  <Input
                    id="scheduled_at"
                    type="datetime-local"
                    value={campaignForm.scheduled_at}
                    onChange={(e) => setCampaignForm({ ...campaignForm, scheduled_at: e.target.value })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  Cancelar
                </Button>
                <Button type="submit">
                  {editingCampaign ? 'Atualizar' : 'Criar Campanha'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Campanhas</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaigns.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ativas</CardTitle>
            <Play className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">
              {campaigns.filter(c => c.status === 'active').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensagens Enviadas</CardTitle>
            <Send className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">
              {campaigns.reduce((acc, c) => acc + c.sent_count, 0)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Leitura</CardTitle>
            <Eye className="h-4 w-4 text-info" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-info">
              {campaigns.reduce((acc, c) => acc + c.sent_count, 0) > 0 
                ? Math.round((campaigns.reduce((acc, c) => acc + c.read_count, 0) / campaigns.reduce((acc, c) => acc + c.sent_count, 0)) * 100)
                : 0}%
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Megaphone className="mr-2 h-5 w-5 text-primary" />
            Lista de Campanhas
          </CardTitle>
          <CardDescription>
            Gerencie suas campanhas de comunicação
          </CardDescription>
        </CardHeader>
        <CardContent>
          {campaigns.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Megaphone className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>Nenhuma campanha criada ainda</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Campanha</TableHead>
                    <TableHead>Canal</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Métricas</TableHead>
                    <TableHead>Criação</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {campaigns.map((campaign) => (
                    <TableRow key={campaign.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{campaign.name}</p>
                          {campaign.description && (
                            <p className="text-sm text-muted-foreground">{campaign.description}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className="mr-2">{getChannelIcon(campaign.channel)}</span>
                          {campaign.channel.toUpperCase()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(campaign.status)}>
                          {campaign.status === 'draft' && 'Rascunho'}
                          {campaign.status === 'active' && 'Ativa'}
                          {campaign.status === 'paused' && 'Pausada'}
                          {campaign.status === 'completed' && 'Concluída'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p>Enviadas: {campaign.sent_count}</p>
                          <p>Entregues: {campaign.delivered_count}</p>
                          <p>Lidas: {campaign.read_count}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {format(new Date(campaign.created_at), 'dd/MM/yy', { locale: ptBR })}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          {campaign.status === 'draft' && (
                            <Button
                              size="sm"
                              variant="success"
                              onClick={() => updateCampaignStatus(campaign.id, 'active')}
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                          )}
                          {campaign.status === 'active' && (
                            <Button
                              size="sm"
                              variant="warning"
                              onClick={() => updateCampaignStatus(campaign.id, 'paused')}
                            >
                              <Pause className="h-4 w-4" />
                            </Button>
                          )}
                          {campaign.status === 'paused' && (
                            <Button
                              size="sm"
                              variant="success"
                              onClick={() => updateCampaignStatus(campaign.id, 'active')}
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(campaign)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDelete(campaign.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignsPage;