(()=>{var e={};e.id=434,e.ids=[434],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>n});var s=t(9866),a=t(4999);async function n(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://zmwdnemlzndjavlriyrc.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set(r,t,s))}catch{}}}})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3171:(e,r,t)=>{"use strict";t.d(r,{Wg:()=>n,hS:()=>u,ru:()=>i});var s=t(2190),a=t(2507);function n(e,r,t=200){return s.NextResponse.json({data:e,error:r},{status:t})}async function i(e,r){try{let e=await (0,a.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return n(void 0,"Unauthorized",401);return await r(t.id,e)}catch(e){return console.error("API Error:",e),n(void 0,e instanceof Error?e.message:"Internal server error",500)}}function u(e){return(console.error("API Error:",e),e?.code==="PGRST116")?n(void 0,"Resource not found",404):e?.code==="23505"?n(void 0,"Resource already exists",409):n(void 0,e?.message||"Internal server error",500)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3338:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>o,POST:()=>c});var a=t(6559),n=t(8088),i=t(7719),u=t(3171);async function o(e){return(0,u.ru)(e,async(e,r)=>{try{let{data:t,error:s}=await r.from("messages").select(`
          *,
          patients(name)
        `).eq("user_id",e).order("created_at",{ascending:!1});if(s)return(0,u.hS)(s);let a=t?.map(e=>({...e,patient_name:e.patients?.name}))||[];return(0,u.Wg)(a)}catch(e){return(0,u.hS)(e)}})}async function c(e){return(0,u.ru)(e,async(r,t)=>{try{let s=await e.json(),a={...s,user_id:r,channel:s.channel||"whatsapp",status:s.status||"sent"},{data:n,error:i}=await t.from("messages").insert(a).select(`
          *,
          patients(name)
        `).single();if(i)return(0,u.hS)(i);let o={...n,patient_name:n.patients?.name};return(0,u.Wg)(o,void 0,201)}catch(e){return(0,u.hS)(e)}})}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/messages/route",pathname:"/api/messages",filename:"route",bundlePath:"app/api/messages/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\messages\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function x(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,866],()=>t(3338));module.exports=s})();