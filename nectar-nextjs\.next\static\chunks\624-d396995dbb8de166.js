(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[624],{1285:(t,e,r)=>{"use strict";r.d(e,{B:()=>l});var a,n=r(2115),s=r(2712),i=(a||(a=r.t(n,2)))[" useId ".trim().toString()]||(()=>void 0),o=0;function l(t){let[e,r]=n.useState(i());return(0,s.N)(()=>{t||r(t=>t??String(o++))},[t]),t||(e?`radix-${e}`:"")}},1362:(t,e,r)=>{"use strict";r.d(e,{D:()=>o});var a=r(2115),n="(prefers-color-scheme: dark)",s=a.createContext(void 0),i={setTheme:t=>{},themes:[]},o=()=>{var t;return null!=(t=a.useContext(s))?t:i},l=null,u=(t,e)=>{let r;try{r=localStorage.getItem(t)||void 0}catch(t){}return r||e},c=t=>{let e=document.createElement("style");return t&&e.setAttribute("nonce",t),e.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},d=t=>(t||(t=window.matchMedia(n)),t.matches?"dark":"light")},1666:t=>{t.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},2712:(t,e,r)=>{"use strict";r.d(e,{N:()=>n});var a=r(2115),n=globalThis?.document?a.useLayoutEffect:()=>{}},2922:(t,e,r)=>{"use strict";r.d(e,{E:()=>q});var a="undefined"==typeof window||"Deno"in globalThis;function n(){}function s(t,e){return"function"==typeof t?t(e):t}function i(t,e){let{type:r="all",exact:a,fetchStatus:n,predicate:s,queryKey:i,stale:o}=t;if(i){if(a){if(e.queryHash!==l(i,e.options))return!1}else if(!c(e.queryKey,i))return!1}if("all"!==r){let t=e.isActive();if("active"===r&&!t||"inactive"===r&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&(!n||n===e.state.fetchStatus)&&(!s||!!s(e))}function o(t,e){let{exact:r,status:a,predicate:n,mutationKey:s}=t;if(s){if(!e.options.mutationKey)return!1;if(r){if(u(e.options.mutationKey)!==u(s))return!1}else if(!c(e.options.mutationKey,s))return!1}return(!a||e.state.status===a)&&(!n||!!n(e))}function l(t,e){return(e?.queryKeyHashFn||u)(t)}function u(t){return JSON.stringify(t,(t,e)=>h(e)?Object.keys(e).sort().reduce((t,r)=>(t[r]=e[r],t),{}):e)}function c(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(r=>c(t[r],e[r]))}function d(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function h(t){if(!f(t))return!1;let e=t.constructor;if(void 0===e)return!0;let r=e.prototype;return!!f(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function f(t){return"[object Object]"===Object.prototype.toString.call(t)}function p(t,e,r=0){let a=[...t,e];return r&&a.length>r?a.slice(1):a}function m(t,e,r=0){let a=[e,...t];return r&&a.length>r?a.slice(0,-1):a}var y=Symbol();function v(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==y?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}var g=t=>setTimeout(t,0),b=function(){let t=[],e=0,r=t=>{t()},a=t=>{t()},n=g,s=a=>{e?t.push(a):n(()=>{r(a)})},i=()=>{let e=t;t=[],e.length&&n(()=>{a(()=>{e.forEach(t=>{r(t)})})})};return{batch:t=>{let r;e++;try{r=t()}finally{--e||i()}return r},batchCalls:t=>(...e)=>{s(()=>{t(...e)})},schedule:s,setNotifyFunction:t=>{r=t},setBatchNotifyFunction:t=>{a=t},setScheduler:t=>{n=t}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},x=new class extends w{#t;#e;#r;constructor(){super(),this.#r=t=>{if(!a&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#r=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:globalThis.document?.visibilityState!=="hidden"}},E=new class extends w{#a=!0;#e;#r;constructor(){super(),this.#r=t=>{if(!a&&window.addEventListener){let e=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#e||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#r=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#a!==t&&(this.#a=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#a}};function C(t){return Math.min(1e3*2**t,3e4)}function T(t){return(t??"online")!=="online"||E.isOnline()}var S=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function P(t){return t instanceof S}function N(t){let e,r=!1,n=0,s=!1,i=function(){let t,e,r=new Promise((r,a)=>{t=r,e=a});function a(t){Object.assign(r,t),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=e=>{a({status:"fulfilled",value:e}),t(e)},r.reject=t=>{a({status:"rejected",reason:t}),e(t)},r}(),o=()=>x.isFocused()&&("always"===t.networkMode||E.isOnline())&&t.canRun(),l=()=>T(t.networkMode)&&t.canRun(),u=r=>{s||(s=!0,t.onSuccess?.(r),e?.(),i.resolve(r))},c=r=>{s||(s=!0,t.onError?.(r),e?.(),i.reject(r))},d=()=>new Promise(r=>{e=t=>{(s||o())&&r(t)},t.onPause?.()}).then(()=>{e=void 0,s||t.onContinue?.()}),h=()=>{let e;if(s)return;let i=0===n?t.initialPromise:void 0;try{e=i??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(u).catch(e=>{if(s)return;let i=t.retry??3*!a,l=t.retryDelay??C,u="function"==typeof l?l(n,e):l,f=!0===i||"number"==typeof i&&n<i||"function"==typeof i&&i(n,e);if(r||!f)return void c(e);n++,t.onFail?.(n,e),new Promise(t=>{setTimeout(t,u)}).then(()=>o()?void 0:d()).then(()=>{r?c(e):h()})})};return{promise:i,cancel:e=>{s||(c(new S(e)),t.abort?.())},continue:()=>(e?.(),i),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:l,start:()=>(l()?h():d().then(h),i)}}var O=class{#n;destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&(this.#n=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(a?1/0:3e5))}clearGcTimeout(){this.#n&&(clearTimeout(this.#n),this.#n=void 0)}},M=class extends O{#s;#i;#o;#l;#u;#c;#d;constructor(t){super(),this.#d=!1,this.#c=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#l=t.client,this.#o=this.#l.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#s=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,r=void 0!==e,a=r?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:r?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#s,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(t){this.options={...this.#c,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(t,e){var r,a;let n=(r=this.state.data,"function"==typeof(a=this.options).structuralSharing?a.structuralSharing(r,t):!1!==a.structuralSharing?function t(e,r){if(e===r)return e;let a=d(e)&&d(r);if(a||h(e)&&h(r)){let n=a?e:Object.keys(e),s=n.length,i=a?r:Object.keys(r),o=i.length,l=a?[]:{},u=new Set(n),c=0;for(let n=0;n<o;n++){let s=a?n:i[n];(!a&&u.has(s)||a)&&void 0===e[s]&&void 0===r[s]?(l[s]=void 0,c++):(l[s]=t(e[s],r[s]),l[s]===e[s]&&void 0!==e[s]&&c++)}return s===o&&c===s?e:l}return r}(r,t):t);return this.#h({data:n,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),n}setState(t,e){this.#h({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#u?.promise;return this.#u?.cancel(t),e?e.then(n).catch(n):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#s)}isActive(){return this.observers.some(t=>{var e;return!1!==(e=t.options.enabled,"function"==typeof e?e(this):e)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===y||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===s(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(t||0)-Date.now(),0))}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#u&&(this.#d?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let r=new AbortController,a=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#d=!0,r.signal)})},n=()=>{let t=v(this.options,e),r=(()=>{let t={client:this.#l,queryKey:this.queryKey,meta:this.meta};return a(t),t})();return(this.#d=!1,this.options.persister)?this.options.persister(t,r,this):t(r)},s=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:n};return a(t),t})();this.options.behavior?.onFetch(s,this),this.#i=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==s.fetchOptions?.meta)&&this.#h({type:"fetch",meta:s.fetchOptions?.meta});let i=t=>{P(t)&&t.silent||this.#h({type:"error",error:t}),P(t)||(this.#o.config.onError?.(t,this),this.#o.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#u=N({initialPromise:e?.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:t=>{if(void 0===t)return void i(Error(`${this.queryHash} data is undefined`));try{this.setData(t)}catch(t){i(t);return}this.#o.config.onSuccess?.(t,this),this.#o.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:i,onFail:(t,e)=>{this.#h({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0}),this.#u.start()}#h(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":var r;return{...e,...(r=e.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:T(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return this.#i=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let a=t.error;if(P(a)&&a.revert&&this.#i)return{...this.#i,fetchStatus:"idle"};return{...e,error:a,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),b.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:t})})}},k=class extends w{constructor(t={}){super(),this.config=t,this.#f=new Map}#f;build(t,e,r){let a=e.queryKey,n=e.queryHash??l(a,e),s=this.get(n);return s||(s=new M({client:t,queryKey:a,queryHash:n,options:t.defaultQueryOptions(e),state:r,defaultOptions:t.getQueryDefaults(a)}),this.add(s)),s}add(t){this.#f.has(t.queryHash)||(this.#f.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let e=this.#f.get(t.queryHash);e&&(t.destroy(),e===t&&this.#f.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){b.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#f.get(t)}getAll(){return[...this.#f.values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>i(e,t))}findAll(t={}){let e=this.getAll();return Object.keys(t).length>0?e.filter(e=>i(t,e)):e}notify(t){b.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){b.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){b.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},R=class extends O{#p;#m;#u;constructor(t){super(),this.mutationId=t.mutationId,this.#m=t.mutationCache,this.#p=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#p.includes(t)||(this.#p.push(t),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#p=this.#p.filter(e=>e!==t),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#h({type:"continue"})};this.#u=N({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#h({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#h({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#m.canRun(this)});let r="pending"===this.state.status,a=!this.#u.canStart();try{if(r)e();else{this.#h({type:"pending",variables:t,isPaused:a}),await this.#m.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#h({type:"pending",context:e,variables:t,isPaused:a})}let n=await this.#u.start();return await this.#m.config.onSuccess?.(n,t,this.state.context,this),await this.options.onSuccess?.(n,t,this.state.context),await this.#m.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,t,this.state.context),this.#h({type:"success",data:n}),n}catch(e){try{throw await this.#m.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#m.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#h({type:"error",error:e})}}finally{this.#m.runNext(this)}}#h(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),b.batch(()=>{this.#p.forEach(e=>{e.onMutationUpdate(t)}),this.#m.notify({mutation:this,type:"updated",action:t})})}},D=class extends w{constructor(t={}){super(),this.config=t,this.#y=new Set,this.#v=new Map,this.#g=0}#y;#v;#g;build(t,e,r){let a=new R({mutationCache:this,mutationId:++this.#g,options:t.defaultMutationOptions(e),state:r});return this.add(a),a}add(t){this.#y.add(t);let e=A(t);if("string"==typeof e){let r=this.#v.get(e);r?r.push(t):this.#v.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#y.delete(t)){let e=A(t);if("string"==typeof e){let r=this.#v.get(e);if(r)if(r.length>1){let e=r.indexOf(t);-1!==e&&r.splice(e,1)}else r[0]===t&&this.#v.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){let e=A(t);if("string"!=typeof e)return!0;{let r=this.#v.get(e),a=r?.find(t=>"pending"===t.state.status);return!a||a===t}}runNext(t){let e=A(t);if("string"!=typeof e)return Promise.resolve();{let r=this.#v.get(e)?.find(e=>e!==t&&e.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){b.batch(()=>{this.#y.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#y.clear(),this.#v.clear()})}getAll(){return Array.from(this.#y)}find(t){let e={exact:!0,...t};return this.getAll().find(t=>o(e,t))}findAll(t={}){return this.getAll().filter(e=>o(t,e))}notify(t){b.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return b.batch(()=>Promise.all(t.map(t=>t.continue().catch(n))))}};function A(t){return t.options.scope?.id}function F(t){return{onFetch:(e,r)=>{let a=e.options,n=e.fetchOptions?.meta?.fetchMore?.direction,s=e.state.data?.pages||[],i=e.state.data?.pageParams||[],o={pages:[],pageParams:[]},l=0,u=async()=>{let r=!1,u=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?r=!0:e.signal.addEventListener("abort",()=>{r=!0}),e.signal)})},c=v(e.options,e.fetchOptions),d=async(t,a,n)=>{if(r)return Promise.reject();if(null==a&&t.pages.length)return Promise.resolve(t);let s=(()=>{let t={client:e.client,queryKey:e.queryKey,pageParam:a,direction:n?"backward":"forward",meta:e.options.meta};return u(t),t})(),i=await c(s),{maxPages:o}=e.options,l=n?m:p;return{pages:l(t.pages,i,o),pageParams:l(t.pageParams,a,o)}};if(n&&s.length){let t="backward"===n,e={pages:s,pageParams:i},r=(t?function(t,{pages:e,pageParams:r}){return e.length>0?t.getPreviousPageParam?.(e[0],e,r[0],r):void 0}:j)(a,e);o=await d(e,r,t)}else{let e=t??s.length;do{let t=0===l?i[0]??a.initialPageParam:j(a,o);if(l>0&&null==t)break;o=await d(o,t),l++}while(l<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(u,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},r):e.fetchFn=u}}}function j(t,{pages:e,pageParams:r}){let a=e.length-1;return e.length>0?t.getNextPageParam(e[a],e,r[a],r):void 0}var q=class{#b;#m;#c;#w;#x;#E;#C;#T;constructor(t={}){this.#b=t.queryCache||new k,this.#m=t.mutationCache||new D,this.#c=t.defaultOptions||{},this.#w=new Map,this.#x=new Map,this.#E=0}mount(){this.#E++,1===this.#E&&(this.#C=x.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#b.onFocus())}),this.#T=E.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#b.onOnline())}))}unmount(){this.#E--,0===this.#E&&(this.#C?.(),this.#C=void 0,this.#T?.(),this.#T=void 0)}isFetching(t){return this.#b.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#m.findAll({...t,status:"pending"}).length}getQueryData(t){let e=this.defaultQueryOptions({queryKey:t});return this.#b.get(e.queryHash)?.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),r=this.#b.build(this,e),a=r.state.data;return void 0===a?this.fetchQuery(t):(t.revalidateIfStale&&r.isStaleByTime(s(e.staleTime,r))&&this.prefetchQuery(e),Promise.resolve(a))}getQueriesData(t){return this.#b.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,r){let a=this.defaultQueryOptions({queryKey:t}),n=this.#b.get(a.queryHash),s=n?.state.data,i="function"==typeof e?e(s):e;if(void 0!==i)return this.#b.build(this,a).setData(i,{...r,manual:!0})}setQueriesData(t,e,r){return b.batch(()=>this.#b.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,r)]))}getQueryState(t){let e=this.defaultQueryOptions({queryKey:t});return this.#b.get(e.queryHash)?.state}removeQueries(t){let e=this.#b;b.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let r=this.#b;return b.batch(()=>(r.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){let r={revert:!0,...e};return Promise.all(b.batch(()=>this.#b.findAll(t).map(t=>t.cancel(r)))).then(n).catch(n)}invalidateQueries(t,e={}){return b.batch(()=>(this.#b.findAll(t).forEach(t=>{t.invalidate()}),t?.refetchType==="none")?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))}refetchQueries(t,e={}){let r={...e,cancelRefetch:e.cancelRefetch??!0};return Promise.all(b.batch(()=>this.#b.findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,r);return r.throwOnError||(e=e.catch(n)),"paused"===t.state.fetchStatus?Promise.resolve():e}))).then(n)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let r=this.#b.build(this,e);return r.isStaleByTime(s(e.staleTime,r))?r.fetch(e):Promise.resolve(r.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(n).catch(n)}fetchInfiniteQuery(t){return t.behavior=F(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(n).catch(n)}ensureInfiniteQueryData(t){return t.behavior=F(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return E.isOnline()?this.#m.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#b}getMutationCache(){return this.#m}getDefaultOptions(){return this.#c}setDefaultOptions(t){this.#c=t}setQueryDefaults(t,e){this.#w.set(u(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...this.#w.values()],r={};return e.forEach(e=>{c(t,e.queryKey)&&Object.assign(r,e.defaultOptions)}),r}setMutationDefaults(t,e){this.#x.set(u(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...this.#x.values()],r={};return e.forEach(e=>{c(t,e.mutationKey)&&Object.assign(r,e.defaultOptions)}),r}defaultQueryOptions(t){if(t._defaulted)return t;let e={...this.#c.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=l(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===y&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#c.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#b.clear(),this.#m.clear()}}},3655:(t,e,r)=>{"use strict";r.d(e,{hO:()=>l,sG:()=>o});var a=r(2115),n=r(7650),s=r(9708),i=r(5155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let r=(0,s.TL)(`Primitive.${e}`),n=a.forwardRef((t,a)=>{let{asChild:n,...s}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:e,{...s,ref:a})});return n.displayName=`Primitive.${e}`,{...t,[e]:n}},{});function l(t,e){t&&n.flushSync(()=>t.dispatchEvent(e))}},5185:(t,e,r)=>{"use strict";function a(t,e,{checkForDefaultPrevented:r=!0}={}){return function(a){if(t?.(a),!1===r||!a.defaultPrevented)return e?.(a)}}r.d(e,{m:()=>a})},5845:(t,e,r)=>{"use strict";r.d(e,{i:()=>o});var a,n=r(2115),s=r(2712),i=(a||(a=r.t(n,2)))[" useInsertionEffect ".trim().toString()]||s.N;function o({prop:t,defaultProp:e,onChange:r=()=>{},caller:a}){let[s,o,l]=function({defaultProp:t,onChange:e}){let[r,a]=n.useState(t),s=n.useRef(r),o=n.useRef(e);return i(()=>{o.current=e},[e]),n.useEffect(()=>{s.current!==r&&(o.current?.(r),s.current=r)},[r,s]),[r,a,o]}({defaultProp:e,onChange:r}),u=void 0!==t,c=u?t:s;{let e=n.useRef(void 0!==t);n.useEffect(()=>{let t=e.current;if(t!==u){let e=u?"controlled":"uncontrolled";console.warn(`${a} is changing from ${t?"controlled":"uncontrolled"} to ${e}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}e.current=u},[u,a])}return[c,n.useCallback(e=>{if(u){let r="function"==typeof e?e(t):e;r!==t&&l.current?.(r)}else o(e)},[u,t,o,l])]}Symbol("RADIX:SYNC_STATE")},6081:(t,e,r)=>{"use strict";r.d(e,{A:()=>i,q:()=>s});var a=r(2115),n=r(5155);function s(t,e){let r=a.createContext(e),s=t=>{let{children:e,...s}=t,i=a.useMemo(()=>s,Object.values(s));return(0,n.jsx)(r.Provider,{value:i,children:e})};return s.displayName=t+"Provider",[s,function(n){let s=a.useContext(r);if(s)return s;if(void 0!==e)return e;throw Error(`\`${n}\` must be used within \`${t}\``)}]}function i(t,e=[]){let r=[],s=()=>{let e=r.map(t=>a.createContext(t));return function(r){let n=r?.[t]||e;return a.useMemo(()=>({[`__scope${t}`]:{...r,[t]:n}}),[r,n])}};return s.scopeName=t,[function(e,s){let i=a.createContext(s),o=r.length;r=[...r,s];let l=e=>{let{scope:r,children:s,...l}=e,u=r?.[t]?.[o]||i,c=a.useMemo(()=>l,Object.values(l));return(0,n.jsx)(u.Provider,{value:c,children:s})};return l.displayName=e+"Provider",[l,function(r,n){let l=n?.[t]?.[o]||i,u=a.useContext(l);if(u)return u;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let r=()=>{let r=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let n=r.reduce((e,{useScope:r,scopeName:a})=>{let n=r(t)[`__scope${a}`];return{...e,...n}},{});return a.useMemo(()=>({[`__scope${e.scopeName}`]:n}),[n])}};return r.scopeName=e.scopeName,r}(s,...e)]}},6621:(t,e,r)=>{"use strict";r.d(e,{Kq:()=>X,LM:()=>W,VY:()=>tt,bL:()=>J,bm:()=>tr,hE:()=>Z,rc:()=>te});var a=r(2115),n=r(7650),s=r(5185),i=r(6101),o=r(7328),l=r(6081),u=r(9178),c=r(4378),d=r(8905),h=r(3655),f=r(9033),p=r(5845),m=r(2712),y=r(2564),v=r(5155),g="ToastProvider",[b,w,x]=(0,o.N)("Toast"),[E,C]=(0,l.A)("Toast",[x]),[T,S]=E(g),P=t=>{let{__scopeToast:e,label:r="Notification",duration:n=5e3,swipeDirection:s="right",swipeThreshold:i=50,children:o}=t,[l,u]=a.useState(null),[c,d]=a.useState(0),h=a.useRef(!1),f=a.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,v.jsx)(b.Provider,{scope:e,children:(0,v.jsx)(T,{scope:e,label:r,duration:n,swipeDirection:s,swipeThreshold:i,toastCount:c,viewport:l,onViewportChange:u,onToastAdd:a.useCallback(()=>d(t=>t+1),[]),onToastRemove:a.useCallback(()=>d(t=>t-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:f,children:o})})};P.displayName=g;var N="ToastViewport",O=["F8"],M="toast.viewportPause",k="toast.viewportResume",R=a.forwardRef((t,e)=>{let{__scopeToast:r,hotkey:n=O,label:s="Notifications ({hotkey})",...o}=t,l=S(N,r),c=w(r),d=a.useRef(null),f=a.useRef(null),p=a.useRef(null),m=a.useRef(null),y=(0,i.s)(e,m,l.onViewportChange),g=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=l.toastCount>0;a.useEffect(()=>{let t=t=>{var e;0!==n.length&&n.every(e=>t[e]||t.code===e)&&(null==(e=m.current)||e.focus())};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[n]),a.useEffect(()=>{let t=d.current,e=m.current;if(x&&t&&e){let r=()=>{if(!l.isClosePausedRef.current){let t=new CustomEvent(M);e.dispatchEvent(t),l.isClosePausedRef.current=!0}},a=()=>{if(l.isClosePausedRef.current){let t=new CustomEvent(k);e.dispatchEvent(t),l.isClosePausedRef.current=!1}},n=e=>{t.contains(e.relatedTarget)||a()},s=()=>{t.contains(document.activeElement)||a()};return t.addEventListener("focusin",r),t.addEventListener("focusout",n),t.addEventListener("pointermove",r),t.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",a),()=>{t.removeEventListener("focusin",r),t.removeEventListener("focusout",n),t.removeEventListener("pointermove",r),t.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",a)}}},[x,l.isClosePausedRef]);let E=a.useCallback(t=>{let{tabbingDirection:e}=t,r=c().map(t=>{let r=t.ref.current,a=[r,...function(t){let e=[],r=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:t=>{let e="INPUT"===t.tagName&&"hidden"===t.type;return t.disabled||t.hidden||e?NodeFilter.FILTER_SKIP:t.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)e.push(r.currentNode);return e}(r)];return"forwards"===e?a:a.reverse()});return("forwards"===e?r.reverse():r).flat()},[c]);return a.useEffect(()=>{let t=m.current;if(t){let e=e=>{let r=e.altKey||e.ctrlKey||e.metaKey;if("Tab"===e.key&&!r){var a,n,s;let r=document.activeElement,i=e.shiftKey;if(e.target===t&&i){null==(a=f.current)||a.focus();return}let o=E({tabbingDirection:i?"backwards":"forwards"}),l=o.findIndex(t=>t===r);$(o.slice(l+1))?e.preventDefault():i?null==(n=f.current)||n.focus():null==(s=p.current)||s.focus()}};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)}},[c,E]),(0,v.jsxs)(u.lg,{ref:d,role:"region","aria-label":s.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,v.jsx)(A,{ref:f,onFocusFromOutsideViewport:()=>{$(E({tabbingDirection:"forwards"}))}}),(0,v.jsx)(b.Slot,{scope:r,children:(0,v.jsx)(h.sG.ol,{tabIndex:-1,...o,ref:y})}),x&&(0,v.jsx)(A,{ref:p,onFocusFromOutsideViewport:()=>{$(E({tabbingDirection:"backwards"}))}})]})});R.displayName=N;var D="ToastFocusProxy",A=a.forwardRef((t,e)=>{let{__scopeToast:r,onFocusFromOutsideViewport:a,...n}=t,s=S(D,r);return(0,v.jsx)(y.s6,{"aria-hidden":!0,tabIndex:0,...n,ref:e,style:{position:"fixed"},onFocus:t=>{var e;let r=t.relatedTarget;(null==(e=s.viewport)?void 0:e.contains(r))||a()}})});A.displayName=D;var F="Toast",j=a.forwardRef((t,e)=>{let{forceMount:r,open:a,defaultOpen:n,onOpenChange:i,...o}=t,[l,u]=(0,p.i)({prop:a,defaultProp:null==n||n,onChange:i,caller:F});return(0,v.jsx)(d.C,{present:r||l,children:(0,v.jsx)(I,{open:l,...o,ref:e,onClose:()=>u(!1),onPause:(0,f.c)(t.onPause),onResume:(0,f.c)(t.onResume),onSwipeStart:(0,s.m)(t.onSwipeStart,t=>{t.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,s.m)(t.onSwipeMove,t=>{let{x:e,y:r}=t.detail.delta;t.currentTarget.setAttribute("data-swipe","move"),t.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(e,"px")),t.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,s.m)(t.onSwipeCancel,t=>{t.currentTarget.setAttribute("data-swipe","cancel"),t.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),t.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),t.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),t.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,s.m)(t.onSwipeEnd,t=>{let{x:e,y:r}=t.detail.delta;t.currentTarget.setAttribute("data-swipe","end"),t.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),t.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),t.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(e,"px")),t.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),u(!1)})})})});j.displayName=F;var[q,L]=E(F,{onClose(){}}),I=a.forwardRef((t,e)=>{let{__scopeToast:r,type:o="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:p,onPause:m,onResume:y,onSwipeStart:g,onSwipeMove:w,onSwipeCancel:x,onSwipeEnd:E,...C}=t,T=S(F,r),[P,N]=a.useState(null),O=(0,i.s)(e,t=>N(t)),R=a.useRef(null),D=a.useRef(null),A=l||T.duration,j=a.useRef(0),L=a.useRef(A),I=a.useRef(0),{onToastAdd:K,onToastRemove:Q}=T,B=(0,f.c)(()=>{var t;(null==P?void 0:P.contains(document.activeElement))&&(null==(t=T.viewport)||t.focus()),d()}),_=a.useCallback(t=>{t&&t!==1/0&&(window.clearTimeout(I.current),j.current=new Date().getTime(),I.current=window.setTimeout(B,t))},[B]);a.useEffect(()=>{let t=T.viewport;if(t){let e=()=>{_(L.current),null==y||y()},r=()=>{let t=new Date().getTime()-j.current;L.current=L.current-t,window.clearTimeout(I.current),null==m||m()};return t.addEventListener(M,r),t.addEventListener(k,e),()=>{t.removeEventListener(M,r),t.removeEventListener(k,e)}}},[T.viewport,A,m,y,_]),a.useEffect(()=>{c&&!T.isClosePausedRef.current&&_(A)},[c,A,T.isClosePausedRef,_]),a.useEffect(()=>(K(),()=>Q()),[K,Q]);let H=a.useMemo(()=>P?function t(e){let r=[];return Array.from(e.childNodes).forEach(e=>{var a;if(e.nodeType===e.TEXT_NODE&&e.textContent&&r.push(e.textContent),(a=e).nodeType===a.ELEMENT_NODE){let a=e.ariaHidden||e.hidden||"none"===e.style.display,n=""===e.dataset.radixToastAnnounceExclude;if(!a)if(n){let t=e.dataset.radixToastAnnounceAlt;t&&r.push(t)}else r.push(...t(e))}}),r}(P):null,[P]);return T.viewport?(0,v.jsxs)(v.Fragment,{children:[H&&(0,v.jsx)(U,{__scopeToast:r,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:H}),(0,v.jsx)(q,{scope:r,onClose:B,children:n.createPortal((0,v.jsx)(b.ItemSlot,{scope:r,children:(0,v.jsx)(u.bL,{asChild:!0,onEscapeKeyDown:(0,s.m)(p,()=>{T.isFocusedToastEscapeKeyDownRef.current||B(),T.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,v.jsx)(h.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":T.swipeDirection,...C,ref:O,style:{userSelect:"none",touchAction:"none",...t.style},onKeyDown:(0,s.m)(t.onKeyDown,t=>{"Escape"===t.key&&(null==p||p(t.nativeEvent),t.nativeEvent.defaultPrevented||(T.isFocusedToastEscapeKeyDownRef.current=!0,B()))}),onPointerDown:(0,s.m)(t.onPointerDown,t=>{0===t.button&&(R.current={x:t.clientX,y:t.clientY})}),onPointerMove:(0,s.m)(t.onPointerMove,t=>{if(!R.current)return;let e=t.clientX-R.current.x,r=t.clientY-R.current.y,a=!!D.current,n=["left","right"].includes(T.swipeDirection),s=["left","up"].includes(T.swipeDirection)?Math.min:Math.max,i=n?s(0,e):0,o=n?0:s(0,r),l="touch"===t.pointerType?10:2,u={x:i,y:o},c={originalEvent:t,delta:u};a?(D.current=u,G("toast.swipeMove",w,c,{discrete:!1})):V(u,T.swipeDirection,l)?(D.current=u,G("toast.swipeStart",g,c,{discrete:!1}),t.target.setPointerCapture(t.pointerId)):(Math.abs(e)>l||Math.abs(r)>l)&&(R.current=null)}),onPointerUp:(0,s.m)(t.onPointerUp,t=>{let e=D.current,r=t.target;if(r.hasPointerCapture(t.pointerId)&&r.releasePointerCapture(t.pointerId),D.current=null,R.current=null,e){let r=t.currentTarget,a={originalEvent:t,delta:e};V(e,T.swipeDirection,T.swipeThreshold)?G("toast.swipeEnd",E,a,{discrete:!0}):G("toast.swipeCancel",x,a,{discrete:!0}),r.addEventListener("click",t=>t.preventDefault(),{once:!0})}})})})}),T.viewport)})]}):null}),U=t=>{let{__scopeToast:e,children:r,...n}=t,s=S(F,e),[i,o]=a.useState(!1),[l,u]=a.useState(!1);return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},e=(0,f.c)(t);(0,m.N)(()=>{let t=0,r=0;return t=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(e)),()=>{window.cancelAnimationFrame(t),window.cancelAnimationFrame(r)}},[e])}(()=>o(!0)),a.useEffect(()=>{let t=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(t)},[]),l?null:(0,v.jsx)(c.Z,{asChild:!0,children:(0,v.jsx)(y.s6,{...n,children:i&&(0,v.jsxs)(v.Fragment,{children:[s.label," ",r]})})})},K=a.forwardRef((t,e)=>{let{__scopeToast:r,...a}=t;return(0,v.jsx)(h.sG.div,{...a,ref:e})});K.displayName="ToastTitle";var Q=a.forwardRef((t,e)=>{let{__scopeToast:r,...a}=t;return(0,v.jsx)(h.sG.div,{...a,ref:e})});Q.displayName="ToastDescription";var B="ToastAction",_=a.forwardRef((t,e)=>{let{altText:r,...a}=t;return r.trim()?(0,v.jsx)(Y,{altText:r,asChild:!0,children:(0,v.jsx)(z,{...a,ref:e})}):(console.error("Invalid prop `altText` supplied to `".concat(B,"`. Expected non-empty `string`.")),null)});_.displayName=B;var H="ToastClose",z=a.forwardRef((t,e)=>{let{__scopeToast:r,...a}=t,n=L(H,r);return(0,v.jsx)(Y,{asChild:!0,children:(0,v.jsx)(h.sG.button,{type:"button",...a,ref:e,onClick:(0,s.m)(t.onClick,n.onClose)})})});z.displayName=H;var Y=a.forwardRef((t,e)=>{let{__scopeToast:r,altText:a,...n}=t;return(0,v.jsx)(h.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":a||void 0,...n,ref:e})});function G(t,e,r,a){let{discrete:n}=a,s=r.originalEvent.currentTarget,i=new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:r});e&&s.addEventListener(t,e,{once:!0}),n?(0,h.hO)(s,i):s.dispatchEvent(i)}var V=function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=Math.abs(t.x),n=Math.abs(t.y),s=a>n;return"left"===e||"right"===e?s&&a>r:!s&&n>r};function $(t){let e=document.activeElement;return t.some(t=>t===e||(t.focus(),document.activeElement!==e))}var X=P,W=R,J=j,Z=K,tt=Q,te=_,tr=z},6671:(t,e,r)=>{"use strict";r.d(e,{l$:()=>E});var a=r(2115),n=r(7650);let s=t=>{switch(t){case"success":return l;case"info":return c;case"warning":return u;case"error":return d;default:return null}},i=Array(12).fill(0),o=t=>{let{visible:e,className:r}=t;return a.createElement("div",{className:["sonner-loading-wrapper",r].filter(Boolean).join(" "),"data-visible":e},a.createElement("div",{className:"sonner-spinner"},i.map((t,e)=>a.createElement("div",{className:"sonner-loading-bar",key:"spinner-bar-".concat(e)}))))},l=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),h=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},a.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),a.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),f=()=>{let[t,e]=a.useState(document.hidden);return a.useEffect(()=>{let t=()=>{e(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)},[]),t},p=1;class m{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);this.subscribers.splice(e,1)}),this.publish=t=>{this.subscribers.forEach(e=>e(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var e;let{message:r,...a}=t,n="number"==typeof(null==t?void 0:t.id)||(null==(e=t.id)?void 0:e.length)>0?t.id:p++,s=this.toasts.find(t=>t.id===n),i=void 0===t.dismissible||t.dismissible;return this.dismissedToasts.has(n)&&this.dismissedToasts.delete(n),s?this.toasts=this.toasts.map(e=>e.id===n?(this.publish({...e,...t,id:n,title:r}),{...e,...t,id:n,dismissible:i,title:r}):e):this.addToast({title:r,...a,dismissible:i,id:n}),n},this.dismiss=t=>(t?(this.dismissedToasts.add(t),requestAnimationFrame(()=>this.subscribers.forEach(e=>e({id:t,dismiss:!0})))):this.toasts.forEach(t=>{this.subscribers.forEach(e=>e({id:t.id,dismiss:!0}))}),t),this.message=(t,e)=>this.create({...e,message:t}),this.error=(t,e)=>this.create({...e,message:t,type:"error"}),this.success=(t,e)=>this.create({...e,type:"success",message:t}),this.info=(t,e)=>this.create({...e,type:"info",message:t}),this.warning=(t,e)=>this.create({...e,type:"warning",message:t}),this.loading=(t,e)=>this.create({...e,type:"loading",message:t}),this.promise=(t,e)=>{let r,n;if(!e)return;void 0!==e.loading&&(n=this.create({...e,promise:t,type:"loading",message:e.loading,description:"function"!=typeof e.description?e.description:void 0}));let s=Promise.resolve(t instanceof Function?t():t),i=void 0!==n,o=s.then(async t=>{if(r=["resolve",t],a.isValidElement(t))i=!1,this.create({id:n,type:"default",message:t});else if(v(t)&&!t.ok){i=!1;let r="function"==typeof e.error?await e.error("HTTP error! status: ".concat(t.status)):e.error,s="function"==typeof e.description?await e.description("HTTP error! status: ".concat(t.status)):e.description,o="object"!=typeof r||a.isValidElement(r)?{message:r}:r;this.create({id:n,type:"error",description:s,...o})}else if(t instanceof Error){i=!1;let r="function"==typeof e.error?await e.error(t):e.error,s="function"==typeof e.description?await e.description(t):e.description,o="object"!=typeof r||a.isValidElement(r)?{message:r}:r;this.create({id:n,type:"error",description:s,...o})}else if(void 0!==e.success){i=!1;let r="function"==typeof e.success?await e.success(t):e.success,s="function"==typeof e.description?await e.description(t):e.description,o="object"!=typeof r||a.isValidElement(r)?{message:r}:r;this.create({id:n,type:"success",description:s,...o})}}).catch(async t=>{if(r=["reject",t],void 0!==e.error){i=!1;let r="function"==typeof e.error?await e.error(t):e.error,s="function"==typeof e.description?await e.description(t):e.description,o="object"!=typeof r||a.isValidElement(r)?{message:r}:r;this.create({id:n,type:"error",description:s,...o})}}).finally(()=>{i&&(this.dismiss(n),n=void 0),null==e.finally||e.finally.call(e)}),l=()=>new Promise((t,e)=>o.then(()=>"reject"===r[0]?e(r[1]):t(r[1])).catch(e));return"string"!=typeof n&&"number"!=typeof n?{unwrap:l}:Object.assign(n,{unwrap:l})},this.custom=(t,e)=>{let r=(null==e?void 0:e.id)||p++;return this.create({jsx:t(r),id:r,...e}),r},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let y=new m,v=t=>t&&"object"==typeof t&&"ok"in t&&"boolean"==typeof t.ok&&"status"in t&&"number"==typeof t.status;function g(t){return void 0!==t.label}function b(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter(Boolean).join(" ")}Object.assign((t,e)=>{let r=(null==e?void 0:e.id)||p++;return y.addToast({title:t,...e,id:r}),r},{success:y.success,info:y.info,warning:y.warning,error:y.error,custom:y.custom,message:y.message,promise:y.promise,dismiss:y.dismiss,loading:y.loading},{getHistory:()=>y.toasts,getToasts:()=>y.getActiveToasts()}),function(t){if(!t||"undefined"==typeof document)return;let e=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",e.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let w=t=>{var e,r,n,i,l,u,c,d,p,m,y;let{invert:v,toast:w,unstyled:x,interacting:E,setHeights:C,visibleToasts:T,heights:S,index:P,toasts:N,expanded:O,removeToast:M,defaultRichColors:k,closeButton:R,style:D,cancelButtonStyle:A,actionButtonStyle:F,className:j="",descriptionClassName:q="",duration:L,position:I,gap:U,expandByDefault:K,classNames:Q,icons:B,closeButtonAriaLabel:_="Close toast"}=t,[H,z]=a.useState(null),[Y,G]=a.useState(null),[V,$]=a.useState(!1),[X,W]=a.useState(!1),[J,Z]=a.useState(!1),[tt,te]=a.useState(!1),[tr,ta]=a.useState(!1),[tn,ts]=a.useState(0),[ti,to]=a.useState(0),tl=a.useRef(w.duration||L||4e3),tu=a.useRef(null),tc=a.useRef(null),td=0===P,th=P+1<=T,tf=w.type,tp=!1!==w.dismissible,tm=w.className||"",ty=w.descriptionClassName||"",tv=a.useMemo(()=>S.findIndex(t=>t.toastId===w.id)||0,[S,w.id]),tg=a.useMemo(()=>{var t;return null!=(t=w.closeButton)?t:R},[w.closeButton,R]),tb=a.useMemo(()=>w.duration||L||4e3,[w.duration,L]),tw=a.useRef(0),tx=a.useRef(0),tE=a.useRef(0),tC=a.useRef(null),[tT,tS]=I.split("-"),tP=a.useMemo(()=>S.reduce((t,e,r)=>r>=tv?t:t+e.height,0),[S,tv]),tN=f(),tO=w.invert||v,tM="loading"===tf;tx.current=a.useMemo(()=>tv*U+tP,[tv,tP]),a.useEffect(()=>{tl.current=tb},[tb]),a.useEffect(()=>{$(!0)},[]),a.useEffect(()=>{let t=tc.current;if(t){let e=t.getBoundingClientRect().height;return to(e),C(t=>[{toastId:w.id,height:e,position:w.position},...t]),()=>C(t=>t.filter(t=>t.toastId!==w.id))}},[C,w.id]),a.useLayoutEffect(()=>{if(!V)return;let t=tc.current,e=t.style.height;t.style.height="auto";let r=t.getBoundingClientRect().height;t.style.height=e,to(r),C(t=>t.find(t=>t.toastId===w.id)?t.map(t=>t.toastId===w.id?{...t,height:r}:t):[{toastId:w.id,height:r,position:w.position},...t])},[V,w.title,w.description,C,w.id,w.jsx,w.action,w.cancel]);let tk=a.useCallback(()=>{W(!0),ts(tx.current),C(t=>t.filter(t=>t.toastId!==w.id)),setTimeout(()=>{M(w)},200)},[w,M,C,tx]);a.useEffect(()=>{let t;if((!w.promise||"loading"!==tf)&&w.duration!==1/0&&"loading"!==w.type)return O||E||tN?(()=>{if(tE.current<tw.current){let t=new Date().getTime()-tw.current;tl.current=tl.current-t}tE.current=new Date().getTime()})():tl.current!==1/0&&(tw.current=new Date().getTime(),t=setTimeout(()=>{null==w.onAutoClose||w.onAutoClose.call(w,w),tk()},tl.current)),()=>clearTimeout(t)},[O,E,w,tf,tN,tk]),a.useEffect(()=>{w.delete&&(tk(),null==w.onDismiss||w.onDismiss.call(w,w))},[tk,w.delete]);let tR=w.icon||(null==B?void 0:B[tf])||s(tf);return a.createElement("li",{tabIndex:0,ref:tc,className:b(j,tm,null==Q?void 0:Q.toast,null==w||null==(e=w.classNames)?void 0:e.toast,null==Q?void 0:Q.default,null==Q?void 0:Q[tf],null==w||null==(r=w.classNames)?void 0:r[tf]),"data-sonner-toast":"","data-rich-colors":null!=(m=w.richColors)?m:k,"data-styled":!(w.jsx||w.unstyled||x),"data-mounted":V,"data-promise":!!w.promise,"data-swiped":tr,"data-removed":X,"data-visible":th,"data-y-position":tT,"data-x-position":tS,"data-index":P,"data-front":td,"data-swiping":J,"data-dismissible":tp,"data-type":tf,"data-invert":tO,"data-swipe-out":tt,"data-swipe-direction":Y,"data-expanded":!!(O||K&&V),style:{"--index":P,"--toasts-before":P,"--z-index":N.length-P,"--offset":"".concat(X?tn:tx.current,"px"),"--initial-height":K?"auto":"".concat(ti,"px"),...D,...w.style},onDragEnd:()=>{Z(!1),z(null),tC.current=null},onPointerDown:t=>{2!==t.button&&!tM&&tp&&(tu.current=new Date,ts(tx.current),t.target.setPointerCapture(t.pointerId),"BUTTON"!==t.target.tagName&&(Z(!0),tC.current={x:t.clientX,y:t.clientY}))},onPointerUp:()=>{var t,e,r,a,n;if(tt||!tp)return;tC.current=null;let s=Number((null==(t=tc.current)?void 0:t.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),i=Number((null==(e=tc.current)?void 0:e.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),o=new Date().getTime()-(null==(r=tu.current)?void 0:r.getTime()),l="x"===H?s:i,u=Math.abs(l)/o;if(Math.abs(l)>=45||u>.11){ts(tx.current),null==w.onDismiss||w.onDismiss.call(w,w),"x"===H?G(s>0?"right":"left"):G(i>0?"down":"up"),tk(),te(!0);return}null==(a=tc.current)||a.style.setProperty("--swipe-amount-x","0px"),null==(n=tc.current)||n.style.setProperty("--swipe-amount-y","0px"),ta(!1),Z(!1),z(null)},onPointerMove:e=>{var r,a,n,s;if(!tC.current||!tp||(null==(r=window.getSelection())?void 0:r.toString().length)>0)return;let i=e.clientY-tC.current.y,o=e.clientX-tC.current.x,l=null!=(s=t.swipeDirections)?s:function(t){let[e,r]=t.split("-"),a=[];return e&&a.push(e),r&&a.push(r),a}(I);!H&&(Math.abs(o)>1||Math.abs(i)>1)&&z(Math.abs(o)>Math.abs(i)?"x":"y");let u={x:0,y:0},c=t=>1/(1.5+Math.abs(t)/20);if("y"===H){if(l.includes("top")||l.includes("bottom"))if(l.includes("top")&&i<0||l.includes("bottom")&&i>0)u.y=i;else{let t=i*c(i);u.y=Math.abs(t)<Math.abs(i)?t:i}}else if("x"===H&&(l.includes("left")||l.includes("right")))if(l.includes("left")&&o<0||l.includes("right")&&o>0)u.x=o;else{let t=o*c(o);u.x=Math.abs(t)<Math.abs(o)?t:o}(Math.abs(u.x)>0||Math.abs(u.y)>0)&&ta(!0),null==(a=tc.current)||a.style.setProperty("--swipe-amount-x","".concat(u.x,"px")),null==(n=tc.current)||n.style.setProperty("--swipe-amount-y","".concat(u.y,"px"))}},tg&&!w.jsx&&"loading"!==tf?a.createElement("button",{"aria-label":_,"data-disabled":tM,"data-close-button":!0,onClick:tM||!tp?()=>{}:()=>{tk(),null==w.onDismiss||w.onDismiss.call(w,w)},className:b(null==Q?void 0:Q.closeButton,null==w||null==(n=w.classNames)?void 0:n.closeButton)},null!=(y=null==B?void 0:B.close)?y:h):null,(tf||w.icon||w.promise)&&null!==w.icon&&((null==B?void 0:B[tf])!==null||w.icon)?a.createElement("div",{"data-icon":"",className:b(null==Q?void 0:Q.icon,null==w||null==(i=w.classNames)?void 0:i.icon)},w.promise||"loading"===w.type&&!w.icon?w.icon||function(){var t,e;return(null==B?void 0:B.loading)?a.createElement("div",{className:b(null==Q?void 0:Q.loader,null==w||null==(e=w.classNames)?void 0:e.loader,"sonner-loader"),"data-visible":"loading"===tf},B.loading):a.createElement(o,{className:b(null==Q?void 0:Q.loader,null==w||null==(t=w.classNames)?void 0:t.loader),visible:"loading"===tf})}():null,"loading"!==w.type?tR:null):null,a.createElement("div",{"data-content":"",className:b(null==Q?void 0:Q.content,null==w||null==(l=w.classNames)?void 0:l.content)},a.createElement("div",{"data-title":"",className:b(null==Q?void 0:Q.title,null==w||null==(u=w.classNames)?void 0:u.title)},w.jsx?w.jsx:"function"==typeof w.title?w.title():w.title),w.description?a.createElement("div",{"data-description":"",className:b(q,ty,null==Q?void 0:Q.description,null==w||null==(c=w.classNames)?void 0:c.description)},"function"==typeof w.description?w.description():w.description):null),a.isValidElement(w.cancel)?w.cancel:w.cancel&&g(w.cancel)?a.createElement("button",{"data-button":!0,"data-cancel":!0,style:w.cancelButtonStyle||A,onClick:t=>{g(w.cancel)&&tp&&(null==w.cancel.onClick||w.cancel.onClick.call(w.cancel,t),tk())},className:b(null==Q?void 0:Q.cancelButton,null==w||null==(d=w.classNames)?void 0:d.cancelButton)},w.cancel.label):null,a.isValidElement(w.action)?w.action:w.action&&g(w.action)?a.createElement("button",{"data-button":!0,"data-action":!0,style:w.actionButtonStyle||F,onClick:t=>{g(w.action)&&(null==w.action.onClick||w.action.onClick.call(w.action,t),t.defaultPrevented||tk())},className:b(null==Q?void 0:Q.actionButton,null==w||null==(p=w.classNames)?void 0:p.actionButton)},w.action.label):null)};function x(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let t=document.documentElement.getAttribute("dir");return"auto"!==t&&t?t:window.getComputedStyle(document.documentElement).direction}let E=a.forwardRef(function(t,e){let{invert:r,position:s="bottom-right",hotkey:i=["altKey","KeyT"],expand:o,closeButton:l,className:u,offset:c,mobileOffset:d,theme:h="light",richColors:f,duration:p,style:m,visibleToasts:v=3,toastOptions:g,dir:b=x(),gap:E=14,icons:C,containerAriaLabel:T="Notifications"}=t,[S,P]=a.useState([]),N=a.useMemo(()=>Array.from(new Set([s].concat(S.filter(t=>t.position).map(t=>t.position)))),[S,s]),[O,M]=a.useState([]),[k,R]=a.useState(!1),[D,A]=a.useState(!1),[F,j]=a.useState("system"!==h?h:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),q=a.useRef(null),L=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),I=a.useRef(null),U=a.useRef(!1),K=a.useCallback(t=>{P(e=>{var r;return(null==(r=e.find(e=>e.id===t.id))?void 0:r.delete)||y.dismiss(t.id),e.filter(e=>{let{id:r}=e;return r!==t.id})})},[]);return a.useEffect(()=>y.subscribe(t=>{if(t.dismiss)return void requestAnimationFrame(()=>{P(e=>e.map(e=>e.id===t.id?{...e,delete:!0}:e))});setTimeout(()=>{n.flushSync(()=>{P(e=>{let r=e.findIndex(e=>e.id===t.id);return -1!==r?[...e.slice(0,r),{...e[r],...t},...e.slice(r+1)]:[t,...e]})})})}),[S]),a.useEffect(()=>{if("system"!==h)return void j(h);if("system"===h&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?j("dark"):j("light")),"undefined"==typeof window)return;let t=window.matchMedia("(prefers-color-scheme: dark)");try{t.addEventListener("change",t=>{let{matches:e}=t;e?j("dark"):j("light")})}catch(e){t.addListener(t=>{let{matches:e}=t;try{e?j("dark"):j("light")}catch(t){console.error(t)}})}},[h]),a.useEffect(()=>{S.length<=1&&R(!1)},[S]),a.useEffect(()=>{let t=t=>{var e,r;i.every(e=>t[e]||t.code===e)&&(R(!0),null==(r=q.current)||r.focus()),"Escape"===t.code&&(document.activeElement===q.current||(null==(e=q.current)?void 0:e.contains(document.activeElement)))&&R(!1)};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[i]),a.useEffect(()=>{if(q.current)return()=>{I.current&&(I.current.focus({preventScroll:!0}),I.current=null,U.current=!1)}},[q.current]),a.createElement("section",{ref:e,"aria-label":"".concat(T," ").concat(L),tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},N.map((e,n)=>{var s;let[i,h]=e.split("-");return S.length?a.createElement("ol",{key:e,dir:"auto"===b?x():b,tabIndex:-1,ref:q,className:u,"data-sonner-toaster":!0,"data-sonner-theme":F,"data-y-position":i,"data-x-position":h,style:{"--front-toast-height":"".concat((null==(s=O[0])?void 0:s.height)||0,"px"),"--width":"".concat(356,"px"),"--gap":"".concat(E,"px"),...m,...function(t,e){let r={};return[t,e].forEach((t,e)=>{let a=1===e,n=a?"--mobile-offset":"--offset",s=a?"16px":"24px";function i(t){["top","right","bottom","left"].forEach(e=>{r["".concat(n,"-").concat(e)]="number"==typeof t?"".concat(t,"px"):t})}"number"==typeof t||"string"==typeof t?i(t):"object"==typeof t?["top","right","bottom","left"].forEach(e=>{void 0===t[e]?r["".concat(n,"-").concat(e)]=s:r["".concat(n,"-").concat(e)]="number"==typeof t[e]?"".concat(t[e],"px"):t[e]}):i(s)}),r}(c,d)},onBlur:t=>{U.current&&!t.currentTarget.contains(t.relatedTarget)&&(U.current=!1,I.current&&(I.current.focus({preventScroll:!0}),I.current=null))},onFocus:t=>{!(t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible)&&(U.current||(U.current=!0,I.current=t.relatedTarget))},onMouseEnter:()=>R(!0),onMouseMove:()=>R(!0),onMouseLeave:()=>{D||R(!1)},onDragEnd:()=>R(!1),onPointerDown:t=>{t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible||A(!0)},onPointerUp:()=>A(!1)},S.filter(t=>!t.position&&0===n||t.position===e).map((n,s)=>{var i,u;return a.createElement(w,{key:n.id,icons:C,index:s,toast:n,defaultRichColors:f,duration:null!=(i=null==g?void 0:g.duration)?i:p,className:null==g?void 0:g.className,descriptionClassName:null==g?void 0:g.descriptionClassName,invert:r,visibleToasts:v,closeButton:null!=(u=null==g?void 0:g.closeButton)?u:l,interacting:D,position:e,style:null==g?void 0:g.style,unstyled:null==g?void 0:g.unstyled,classNames:null==g?void 0:g.classNames,cancelButtonStyle:null==g?void 0:g.cancelButtonStyle,actionButtonStyle:null==g?void 0:g.actionButtonStyle,closeButtonAriaLabel:null==g?void 0:g.closeButtonAriaLabel,removeToast:K,toasts:S.filter(t=>t.position==n.position),heights:O.filter(t=>t.position==n.position),setHeights:M,expandByDefault:o,gap:E,expanded:k,swipeDirections:t.swipeDirections})})):null}))})},6715:(t,e,r)=>{"use strict";r.d(e,{Ht:()=>i});var a=r(2115),n=r(5155),s=a.createContext(void 0),i=t=>{let{client:e,children:r}=t;return a.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,n.jsx)(s.Provider,{value:e,children:r})}},8905:(t,e,r)=>{"use strict";r.d(e,{C:()=>i});var a=r(2115),n=r(6101),s=r(2712),i=t=>{let{present:e,children:r}=t,i=function(t){var e,r;let[n,i]=a.useState(),l=a.useRef(null),u=a.useRef(t),c=a.useRef("none"),[d,h]=(e=t?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((t,e)=>{let a=r[t][e];return null!=a?a:t},e));return a.useEffect(()=>{let t=o(l.current);c.current="mounted"===d?t:"none"},[d]),(0,s.N)(()=>{let e=l.current,r=u.current;if(r!==t){let a=c.current,n=o(e);t?h("MOUNT"):"none"===n||(null==e?void 0:e.display)==="none"?h("UNMOUNT"):r&&a!==n?h("ANIMATION_OUT"):h("UNMOUNT"),u.current=t}},[t,h]),(0,s.N)(()=>{if(n){var t;let e,r=null!=(t=n.ownerDocument.defaultView)?t:window,a=t=>{let a=o(l.current).includes(t.animationName);if(t.target===n&&a&&(h("ANIMATION_END"),!u.current)){let t=n.style.animationFillMode;n.style.animationFillMode="forwards",e=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=t)})}},s=t=>{t.target===n&&(c.current=o(l.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",a),n.addEventListener("animationend",a),()=>{r.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",a),n.removeEventListener("animationend",a)}}h("ANIMATION_END")},[n,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:a.useCallback(t=>{l.current=t?getComputedStyle(t):null,i(t)},[])}}(e),l="function"==typeof r?r({present:i.isPresent}):a.Children.only(r),u=(0,n.s)(i.ref,function(t){var e,r;let a=null==(e=Object.getOwnPropertyDescriptor(t.props,"ref"))?void 0:e.get,n=a&&"isReactWarning"in a&&a.isReactWarning;return n?t.ref:(n=(a=null==(r=Object.getOwnPropertyDescriptor(t,"ref"))?void 0:r.get)&&"isReactWarning"in a&&a.isReactWarning)?t.props.ref:t.props.ref||t.ref}(l));return"function"==typeof r||i.isPresent?a.cloneElement(l,{ref:u}):null};function o(t){return(null==t?void 0:t.animationName)||"none"}i.displayName="Presence"},9033:(t,e,r)=>{"use strict";r.d(e,{c:()=>n});var a=r(2115);function n(t){let e=a.useRef(t);return a.useEffect(()=>{e.current=t}),a.useMemo(()=>(...t)=>e.current?.(...t),[])}}}]);