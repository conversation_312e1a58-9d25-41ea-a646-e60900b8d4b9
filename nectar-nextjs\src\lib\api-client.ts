import { createClient } from '@/lib/supabase/client'

export async function makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
  const supabase = createClient()
  const { data: { session } } = await supabase.auth.getSession()
  
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  }
  
  // Add authorization header if we have a session
  if (session?.access_token) {
    headers['Authorization'] = `Bearer ${session.access_token}`
  }
  
  return fetch(url, {
    ...options,
    headers,
    credentials: 'include', // Include cookies
  })
}
