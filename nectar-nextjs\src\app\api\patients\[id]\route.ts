import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesUpdate } from '@/types/supabase'

type Patient = Tables<'patients'>
type PatientUpdate = TablesUpdate<'patients'>

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const { data: patient, error } = await supabase
        .from('patients')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(patient)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return with<PERSON>uth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const updateData: PatientUpdate = {
        ...body,
        birth_date: body.birth_date || null
      }

      const { data: patient, error } = await supabase
        .from('patients')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(patient)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const { error } = await supabase
        .from('patients')
        .delete()
        .eq('id', id)
        .eq('user_id', userId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ success: true })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
