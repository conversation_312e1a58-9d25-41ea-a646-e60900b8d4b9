(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,a,t)=>{"use strict";t.d(a,{$:()=>o});var s=t(5155),r=t(2115),i=t(9708),n=t(2085),d=t(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",hero:"bg-gradient-primary text-primary-foreground hover:shadow-medium transition-all duration-300 hover:scale-105",success:"bg-success text-success-foreground hover:bg-success/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,a)=>{let{className:t,variant:r,size:n,asChild:o=!1,...c}=e,u=o?i.DX:"button";return(0,s.jsx)(u,{className:(0,d.cn)(l({variant:r,size:n,className:t})),ref:a,...c})});o.displayName="Button"},2523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var s=t(5155),r=t(2115),i=t(9434);let n=r.forwardRef((e,a)=>{let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...n})});n.displayName="Input"},4394:(e,a,t)=>{Promise.resolve().then(t.bind(t,6837))},4771:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var s=t(2115),r=t(3865),i=t(7481);function n(){let[e,a]=(0,s.useState)(null),[t,n]=(0,s.useState)(null),[d,l]=(0,s.useState)(!0),{toast:o}=(0,i.dj)();return(0,s.useEffect)(()=>{let e=(0,r.createBrowserClient)("https://zmwdnemlzndjavlriyrc.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM"),{data:{subscription:t}}=e.auth.onAuthStateChange((e,t)=>{var s;n(t),a(null!=(s=null==t?void 0:t.user)?s:null),l(!1)});return e.auth.getSession().then(e=>{var t;let{data:{session:s}}=e;n(s),a(null!=(t=null==s?void 0:s.user)?t:null),l(!1)}),()=>t.unsubscribe()},[]),{user:e,session:t,loading:d,signIn:async(e,a)=>{try{let t=await fetch("/api/auth/signin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:a})}),s=await t.json();if(!t.ok)return o({title:"Erro no login",description:s.error,variant:"destructive"}),{error:s.error};return{error:null}}catch(e){return o({title:"Erro no login",description:"Erro inesperado ao fazer login",variant:"destructive"}),{error:e}}},signUp:async(e,a,t)=>{try{let s=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:a,name:t})}),r=await s.json();if(!s.ok)return o({title:"Erro no cadastro",description:r.error,variant:"destructive"}),{error:r.error};return o({title:"Cadastro realizado",description:r.message||"Verifique seu email para confirmar a conta"}),{error:null}}catch(e){return o({title:"Erro no cadastro",description:"Erro inesperado ao fazer cadastro",variant:"destructive"}),{error:e}}},signOut:async()=>{try{await fetch("/api/auth/signout",{method:"POST"}),o({title:"Logout realizado",description:"Voc\xea foi desconectado com sucesso"})}catch(e){console.error("Error signing out:",e)}}}}},6102:(e,a,t)=>{"use strict";t.d(a,{Bc:()=>d,ZI:()=>c,k$:()=>o,m_:()=>l});var s=t(5155),r=t(2115),i=t(1764),n=t(9434);let d=i.Kq,l=i.bL,o=i.l9,c=r.forwardRef((e,a)=>{let{className:t,sideOffset:r=4,...d}=e;return(0,s.jsx)(i.UC,{ref:a,sideOffset:r,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...d})});c.displayName=i.UC.displayName},6695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>d});var s=t(5155),r=t(2115),i=t(9434);let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});n.displayName="Card";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...r})});d.displayName="CardHeader";let l=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});l.displayName="CardTitle";let o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",t),...r})});c.displayName="CardContent",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},6837:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>et});var s=t(5155),r=t(2115),i=t(6695),n=t(285),d=t(9074),l=t(9881),o=t(7580),c=t(1497),u=t(5339),m=t(2713),f=t(3109),p=t(4186),x=t(646),h=t(4771),b=t(9708),g=t(2085),v=t(2432),j=t(9434),N=t(2523),w=t(7489);let y=r.forwardRef((e,a)=>{let{className:t,orientation:r="horizontal",decorative:i=!0,...n}=e;return(0,s.jsx)(w.b,{ref:a,decorative:i,orientation:r,className:(0,j.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",t),...n})});y.displayName=w.b.displayName;var S=t(3651),R=t(4416);let A=S.bL;S.l9,S.bm;let k=S.ZL,z=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(S.hJ,{className:(0,j.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:a})});z.displayName=S.hJ.displayName;let C=(0,g.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),_=r.forwardRef((e,a)=>{let{side:t="right",className:r,children:i,...n}=e;return(0,s.jsxs)(k,{children:[(0,s.jsx)(z,{}),(0,s.jsxs)(S.UC,{ref:a,className:(0,j.cn)(C({side:t}),r),...n,children:[i,(0,s.jsxs)(S.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,s.jsx)(R.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});function T(e){let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,j.cn)("animate-pulse rounded-md bg-muted",a),...t})}_.displayName=S.UC.displayName,r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(S.hE,{ref:a,className:(0,j.cn)("text-lg font-semibold text-foreground",t),...r})}).displayName=S.hE.displayName,r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(S.VY,{ref:a,className:(0,j.cn)("text-sm text-muted-foreground",t),...r})}).displayName=S.VY.displayName;var E=t(6102);let I=r.createContext(null);function M(){let e=r.useContext(I);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let O=r.forwardRef((e,a)=>{let{defaultOpen:t=!0,open:i,onOpenChange:n,className:d,style:l,children:o,...c}=e,u=function(){let[e,a]=r.useState(void 0);return r.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),t=()=>{a(window.innerWidth<768)};return e.addEventListener("change",t),a(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[m,f]=r.useState(!1),[p,x]=r.useState(t),h=null!=i?i:p,b=r.useCallback(e=>{let a="function"==typeof e?e(h):e;n?n(a):x(a),document.cookie="".concat("sidebar:state","=").concat(a,"; path=/; max-age=").concat(604800)},[n,h]),g=r.useCallback(()=>u?f(e=>!e):b(e=>!e),[u,b,f]);r.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let v=h?"expanded":"collapsed",N=r.useMemo(()=>({state:v,open:h,setOpen:b,isMobile:u,openMobile:m,setOpenMobile:f,toggleSidebar:g}),[v,h,b,u,m,f,g]);return(0,s.jsx)(I.Provider,{value:N,children:(0,s.jsx)(E.Bc,{delayDuration:0,children:(0,s.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...l},className:(0,j.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",d),ref:a,...c,children:o})})})});O.displayName="SidebarProvider";let Z=r.forwardRef((e,a)=>{let{side:t="left",variant:r="sidebar",collapsible:i="offcanvas",className:n,children:d,...l}=e,{isMobile:o,state:c,openMobile:u,setOpenMobile:m}=M();return"none"===i?(0,s.jsx)("div",{className:(0,j.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",n),ref:a,...l,children:d}):o?(0,s.jsx)(A,{open:u,onOpenChange:m,...l,children:(0,s.jsx)(_,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:d})})}):(0,s.jsxs)("div",{ref:a,className:"group peer hidden md:block text-sidebar-foreground","data-state":c,"data-collapsible":"collapsed"===c?i:"","data-variant":r,"data-side":t,children:[(0,s.jsx)("div",{className:(0,j.cn)("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===r||"inset"===r?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,s.jsx)("div",{className:(0,j.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===r||"inset"===r?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...l,children:(0,s.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:d})})]})});Z.displayName="Sidebar";let B=r.forwardRef((e,a)=>{let{className:t,onClick:r,...i}=e,{toggleSidebar:d}=M();return(0,s.jsxs)(n.$,{ref:a,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,j.cn)("h-7 w-7",t),onClick:e=>{null==r||r(e),d()},...i,children:[(0,s.jsx)(v.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});B.displayName="SidebarTrigger",r.forwardRef((e,a)=>{let{className:t,...r}=e,{toggleSidebar:i}=M();return(0,s.jsx)("button",{ref:a,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:i,title:"Toggle Sidebar",className:(0,j.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...r})}).displayName="SidebarRail",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("main",{ref:a,className:(0,j.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",t),...r})}).displayName="SidebarInset",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(N.p,{ref:a,"data-sidebar":"input",className:(0,j.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",t),...r})}).displayName="SidebarInput",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,"data-sidebar":"header",className:(0,j.cn)("flex flex-col gap-2 p-2",t),...r})}).displayName="SidebarHeader",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,"data-sidebar":"footer",className:(0,j.cn)("flex flex-col gap-2 p-2",t),...r})}).displayName="SidebarFooter",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(y,{ref:a,"data-sidebar":"separator",className:(0,j.cn)("mx-2 w-auto bg-sidebar-border",t),...r})}).displayName="SidebarSeparator";let P=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,"data-sidebar":"content",className:(0,j.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...r})});P.displayName="SidebarContent";let D=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,"data-sidebar":"group",className:(0,j.cn)("relative flex w-full min-w-0 flex-col p-2",t),...r})});D.displayName="SidebarGroup";let J=r.forwardRef((e,a)=>{let{className:t,asChild:r=!1,...i}=e,n=r?b.DX:"div";return(0,s.jsx)(n,{ref:a,"data-sidebar":"group-label",className:(0,j.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...i})});J.displayName="SidebarGroupLabel",r.forwardRef((e,a)=>{let{className:t,asChild:r=!1,...i}=e,n=r?b.DX:"button";return(0,s.jsx)(n,{ref:a,"data-sidebar":"group-action",className:(0,j.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarGroupAction";let V=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,"data-sidebar":"group-content",className:(0,j.cn)("w-full text-sm",t),...r})});V.displayName="SidebarGroupContent";let X=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("ul",{ref:a,"data-sidebar":"menu",className:(0,j.cn)("flex w-full min-w-0 flex-col gap-1",t),...r})});X.displayName="SidebarMenu";let L=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("li",{ref:a,"data-sidebar":"menu-item",className:(0,j.cn)("group/menu-item relative",t),...r})});L.displayName="SidebarMenuItem";let $=(0,g.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),W=r.forwardRef((e,a)=>{let{asChild:t=!1,isActive:r=!1,variant:i="default",size:n="default",tooltip:d,className:l,...o}=e,c=t?b.DX:"button",{isMobile:u,state:m}=M(),f=(0,s.jsx)(c,{ref:a,"data-sidebar":"menu-button","data-size":n,"data-active":r,className:(0,j.cn)($({variant:i,size:n}),l),...o});return d?("string"==typeof d&&(d={children:d}),(0,s.jsxs)(E.m_,{children:[(0,s.jsx)(E.k$,{asChild:!0,children:f}),(0,s.jsx)(E.ZI,{side:"right",align:"center",hidden:"collapsed"!==m||u,...d})]})):f});W.displayName="SidebarMenuButton",r.forwardRef((e,a)=>{let{className:t,asChild:r=!1,showOnHover:i=!1,...n}=e,d=r?b.DX:"button";return(0,s.jsx)(d,{ref:a,"data-sidebar":"menu-action",className:(0,j.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",i&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",t),...n})}).displayName="SidebarMenuAction",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,"data-sidebar":"menu-badge",className:(0,j.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t),...r})}).displayName="SidebarMenuBadge",r.forwardRef((e,a)=>{let{className:t,showIcon:i=!1,...n}=e,d=r.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,s.jsxs)("div",{ref:a,"data-sidebar":"menu-skeleton",className:(0,j.cn)("rounded-md h-8 flex gap-2 px-2 items-center",t),...n,children:[i&&(0,s.jsx)(T,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,s.jsx)(T,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":d}})]})}).displayName="SidebarMenuSkeleton",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("ul",{ref:a,"data-sidebar":"menu-sub",className:(0,j.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...r})}).displayName="SidebarMenuSub",r.forwardRef((e,a)=>{let{...t}=e;return(0,s.jsx)("li",{ref:a,...t})}).displayName="SidebarMenuSubItem",r.forwardRef((e,a)=>{let{asChild:t=!1,size:r="md",isActive:i,className:n,...d}=e,l=t?b.DX:"a";return(0,s.jsx)(l,{ref:a,"data-sidebar":"menu-sub-button","data-size":r,"data-active":i,className:(0,j.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===r&&"text-xs","md"===r&&"text-sm","group-data-[collapsible=icon]:hidden",n),...d})}).displayName="SidebarMenuSubButton";var F=t(6874),U=t.n(F),G=t(5695),q=t(3783),H=t(3062),K=t(381),Q=t(1976);let Y=[{title:"Dashboard",url:"/dashboard",icon:q.A},{title:"Agenda",url:"/agenda",icon:d.A},{title:"Pacientes",url:"/pacientes",icon:o.A},{title:"Mensagens",url:"/mensagens",icon:c.A},{title:"Campanhas",url:"/campanhas",icon:H.A},{title:"Relat\xf3rios",url:"/relatorios",icon:m.A},{title:"Configura\xe7\xf5es",url:"/configuracoes",icon:K.A}];function ee(){let{state:e}=M(),a=(0,G.usePathname)(),t=e=>a===e;return(0,s.jsx)(Z,{collapsible:"icon",children:(0,s.jsxs)(P,{children:[(0,s.jsx)("div",{className:"p-6 border-b",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(Q.A,{className:"h-8 w-8 text-primary mr-2"}),"expanded"===e&&(0,s.jsx)("span",{className:"text-xl font-bold text-foreground",children:"Nectar Sa\xfade"})]})}),(0,s.jsxs)(D,{children:[(0,s.jsx)(J,{children:"Menu Principal"}),(0,s.jsx)(V,{children:(0,s.jsx)(X,{children:Y.map(a=>(0,s.jsx)(L,{children:(0,s.jsx)(W,{asChild:!0,children:(0,s.jsxs)(U(),{href:a.url,className:t(a.url)?"bg-primary/10 text-primary font-medium border-r-2 border-primary":"hover:bg-muted/50",children:[(0,s.jsx)(a.icon,{className:"mr-3 h-5 w-5"}),"expanded"===e&&(0,s.jsx)("span",{children:a.title})]})})},a.title))})})]})]})})}let ea=e=>{let{children:a}=e,{signOut:t}=(0,h.A)();return(0,s.jsx)(O,{children:(0,s.jsxs)("div",{className:"min-h-screen flex w-full bg-background",children:[(0,s.jsx)(ee,{}),(0,s.jsxs)("main",{className:"flex-1",children:[(0,s.jsxs)("header",{className:"h-16 border-b bg-card/50 backdrop-blur-sm flex items-center justify-between px-6",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(B,{className:"mr-4"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-success rounded-full"}),(0,s.jsx)("span",{children:"Online"})]}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:t,children:"Sair"})]})]}),(0,s.jsx)("div",{className:"p-6",children:a})]})]})})};function et(){let[e,a]=(0,r.useState)(null),[t,b]=(0,r.useState)(!0),{user:g}=(0,h.A)();(0,r.useEffect)(()=>{g&&v()},[g]);let v=async()=>{try{let e=await fetch("/api/dashboard/stats");if(e.ok){let t=await e.json();a(t.data)}}catch(e){console.error("Error fetching dashboard data:",e)}finally{b(!1)}};if(t)return(0,s.jsx)(ea,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})});let j=(null==e?void 0:e.stats)||{todayAppointments:0,totalPatients:0,unreadMessages:0,monthlyRevenue:0},N=(null==e?void 0:e.recentAppointments)||[];return(0,s.jsx)(ea,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Vis\xe3o geral dos seus atendimentos e m\xe9tricas"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)(i.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Consultas Hoje"}),(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:j.todayAppointments}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:[(0,s.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"Consultas do dia"]})]})]}),(0,s.jsxs)(i.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Pacientes Ativos"}),(0,s.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:j.totalPatients}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:[(0,s.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"Total cadastrados"]})]})]}),(0,s.jsxs)(i.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Mensagens"}),(0,s.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:"23"}),(0,s.jsxs)("p",{className:"text-xs text-yellow-600 flex items-center mt-1",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}),j.unreadMessages," n\xe3o lidas"]})]})]}),(0,s.jsxs)(i.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Receita Mensal"}),(0,s.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-foreground",children:["R$ ",j.monthlyRevenue.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"+15% vs m\xeas anterior"]})]})]})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"mr-2 h-5 w-5 text-primary"}),"Pr\xf3ximas Consultas"]}),(0,s.jsx)(i.BT,{children:"Agendamentos para hoje e amanh\xe3"})]}),(0,s.jsx)(i.Wu,{className:"space-y-4",children:0===N.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(d.A,{className:"mx-auto h-12 w-12 mb-4 opacity-50"}),(0,s.jsx)("p",{children:"Nenhuma consulta agendada para hoje"})]}):N.map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border bg-card/50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-12 h-12 rounded-full bg-primary/10",children:(0,s.jsx)(p.A,{className:"h-5 w-5 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:e.patient}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.type})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-foreground",children:e.time}),"confirmed"===e.status?(0,s.jsx)(x.A,{className:"h-4 w-4 text-green-500"}):(0,s.jsx)(u.A,{className:"h-4 w-4 text-yellow-500"})]})]},a))})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center",children:[(0,s.jsx)(c.A,{className:"mr-2 h-5 w-5 text-primary"}),"Atividades Recentes"]}),(0,s.jsx)(i.BT,{children:"\xdaltimas intera\xe7\xf5es"})]}),(0,s.jsx)(i.Wu,{className:"space-y-4",children:[{type:"message",text:"Nova mensagem recebida",time:"2 min atr\xe1s"},{type:"appointment",text:"Consulta confirmada",time:"15 min atr\xe1s"},{type:"payment",text:"Pagamento recebido - R$ 150",time:"1 hora atr\xe1s"},{type:"reminder",text:"Lembrete enviado",time:"2 horas atr\xe1s"},{type:"appointment",text:"Nova consulta agendada",time:"3 horas atr\xe1s"}].map((e,a)=>(0,s.jsxs)("div",{className:"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full mt-2 ".concat("message"===e.type?"bg-blue-500":"appointment"===e.type?"bg-green-500":"payment"===e.type?"bg-primary":"bg-yellow-500")}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm text-foreground",children:e.text}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.time})]})]},a))})]})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"A\xe7\xf5es R\xe1pidas"}),(0,s.jsx)(i.BT,{children:"Principais funcionalidades ao seu alcance"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)(n.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(d.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Nova Consulta"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(o.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Cadastrar Paciente"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(c.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Enviar Mensagem"})]}),(0,s.jsxs)(n.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(m.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Ver Relat\xf3rios"})]})]})})]})]})})}},7481:(e,a,t)=>{"use strict";t.d(a,{dj:()=>m});var s=t(2115);let r=0,i=new Map,n=e=>{if(i.has(e))return;let a=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,a)},d=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=a;return t?n(t):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},l=[],o={toasts:[]};function c(e){o=d(o,e),l.forEach(e=>{e(o)})}function u(e){let{...a}=e,t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...a,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,a]=s.useState(o);return s.useEffect(()=>(l.push(a),()=>{let e=l.indexOf(a);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var s=t(2596),r=t(9688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}}},e=>{var a=a=>e(e.s=a);e.O(0,[455,801,573,451,445,441,684,358],()=>a(4394)),_N_E=e.O()}]);