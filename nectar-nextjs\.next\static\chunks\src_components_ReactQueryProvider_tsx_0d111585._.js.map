{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ReactQueryProvider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { useState } from \"react\";\n\nexport function ReactQueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(() => new QueryClient({\n    defaultOptions: {\n      queries: {\n        staleTime: 60 * 1000, // 1 minute\n        retry: 1,\n      },\n    },\n  }));\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAKO,SAAS,mBAAmB,EAAE,QAAQ,EAAiC;;IAC5E,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;uCAAE,IAAM,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACnD,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,OAAO;oBACT;gBACF;YACF;;IAEA,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAGP;GAfgB;KAAA", "debugId": null}}]}