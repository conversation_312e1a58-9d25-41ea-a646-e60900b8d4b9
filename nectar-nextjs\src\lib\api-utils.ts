import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

export function createApiResponse<T>(
  data?: T,
  error?: string,
  status: number = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      data,
      error,
    },
    { status }
  )
}

export async function withAuth<T>(
  request: NextRequest,
  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>
): Promise<NextResponse<ApiResponse<T>>> {
  try {
    const supabase = await createClient()
    
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createApiResponse(undefined, 'Unauthorized', 401)
    }

    return await handler(user.id, supabase)
  } catch (error) {
    console.error('API Error:', error)
    return createApiResponse(
      undefined,
      error instanceof Error ? error.message : 'Internal server error',
      500
    )
  }
}

export function handleApiError(error: any): NextResponse<ApiResponse> {
  console.error('API Error:', error)
  
  if (error?.code === 'PGRST116') {
    return createApiResponse(undefined, 'Resource not found', 404)
  }
  
  if (error?.code === '23505') {
    return createApiResponse(undefined, 'Resource already exists', 409)
  }
  
  return createApiResponse(
    undefined,
    error?.message || 'Internal server error',
    500
  )
}
