import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MessageSquare, Send, Phone, Mail, Plus, Search, Filter } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useSupabase } from '@/hooks/useSupabase';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

type Message = {
  id: string;
  content: string;
  channel: 'whatsapp' | 'email' | 'sms' | 'internal';
  direction: 'inbound' | 'outbound';
  status: 'sent' | 'delivered' | 'read' | 'failed';
  created_at: string;
  patient_name?: string;
  patient_id?: string;
};

type Patient = {
  id: string;
  name: string;
  phone?: string;
  email?: string;
};

const MessagesPage = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterChannel, setFilterChannel] = useState<string>('all');
  const { user } = useSupabase();
  const { toast } = useToast();

  const [messageForm, setMessageForm] = useState({
    patient_id: '',
    content: '',
    channel: 'whatsapp' as const
  });

  useEffect(() => {
    if (user) {
      fetchMessages();
      fetchPatients();
    }
  }, [user]);

  const fetchMessages = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('messages')
      .select(`
        *,
        patients(name)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      toast({
        title: "Erro ao carregar mensagens",
        description: error.message,
        variant: "destructive"
      });
    } else {
      setMessages(data?.map(msg => ({
        ...msg,
        patient_name: msg.patients?.name
      })) || []);
    }
    setLoading(false);
  };

  const fetchPatients = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('patients')
      .select('id, name, phone, email')
      .eq('user_id', user.id)
      .order('name');

    if (error) {
      toast({
        title: "Erro ao carregar pacientes",
        description: error.message,
        variant: "destructive"
      });
    } else {
      setPatients(data || []);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    const { error } = await supabase
      .from('messages')
      .insert({
        user_id: user.id,
        patient_id: messageForm.patient_id || null,
        content: messageForm.content,
        channel: messageForm.channel,
        direction: 'outbound'
      });

    if (error) {
      toast({
        title: "Erro ao enviar mensagem",
        description: error.message,
        variant: "destructive"
      });
    } else {
      toast({
        title: "Mensagem enviada",
        description: "A mensagem foi enviada com sucesso"
      });
      setDialogOpen(false);
      setMessageForm({
        patient_id: '',
        content: '',
        channel: 'whatsapp'
      });
      fetchMessages();
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp': return '📱';
      case 'email': return '📧';
      case 'sms': return '💬';
      default: return '💭';
    }
  };

  const getChannelColor = (channel: string) => {
    switch (channel) {
      case 'whatsapp': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'email': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'sms': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'delivered': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'read': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.patient_name?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesChannel = filterChannel === 'all' || message.channel === filterChannel;
    return matchesSearch && matchesChannel;
  });

  const inboundMessages = filteredMessages.filter(msg => msg.direction === 'inbound');
  const outboundMessages = filteredMessages.filter(msg => msg.direction === 'outbound');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Mensagens</h1>
          <p className="text-muted-foreground">Gerencie comunicações com pacientes</p>
        </div>
        
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Nova Mensagem
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <form onSubmit={handleSendMessage}>
              <DialogHeader>
                <DialogTitle>Enviar Nova Mensagem</DialogTitle>
                <DialogDescription>
                  Envie uma mensagem para um paciente
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <label htmlFor="patient">Paciente (opcional)</label>
                  <Select
                    value={messageForm.patient_id}
                    onValueChange={(value) => setMessageForm({ ...messageForm, patient_id: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um paciente" />
                    </SelectTrigger>
                    <SelectContent>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label htmlFor="channel">Canal</label>
                  <Select
                    value={messageForm.channel}
                    onValueChange={(value: any) => setMessageForm({ ...messageForm, channel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="whatsapp">📱 WhatsApp</SelectItem>
                      <SelectItem value="email">📧 Email</SelectItem>
                      <SelectItem value="sms">💬 SMS</SelectItem>
                      <SelectItem value="internal">💭 Interna</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label htmlFor="content">Mensagem</label>
                  <Textarea
                    id="content"
                    value={messageForm.content}
                    onChange={(e) => setMessageForm({ ...messageForm, content: e.target.value })}
                    required
                    rows={4}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">
                  <Send className="mr-2 h-4 w-4" />
                  Enviar Mensagem
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="mr-2 h-5 w-5 text-primary" />
            Central de Mensagens
          </CardTitle>
          <CardDescription>
            Total: {messages.length} mensagem(ns)
          </CardDescription>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar mensagens..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={filterChannel} onValueChange={setFilterChannel}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="whatsapp">WhatsApp</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="sms">SMS</SelectItem>
                  <SelectItem value="internal">Interno</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">Todas ({filteredMessages.length})</TabsTrigger>
              <TabsTrigger value="received">Recebidas ({inboundMessages.length})</TabsTrigger>
              <TabsTrigger value="sent">Enviadas ({outboundMessages.length})</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="space-y-4">
              {filteredMessages.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhuma mensagem encontrada</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-4 rounded-lg border ${
                        message.direction === 'outbound' 
                          ? 'bg-primary/5 ml-8' 
                          : 'bg-card/50 mr-8'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getChannelIcon(message.channel)}</span>
                          <Badge className={getChannelColor(message.channel)}>
                            {message.channel.toUpperCase()}
                          </Badge>
                          <Badge className={getStatusColor(message.status)}>
                            {message.status.toUpperCase()}
                          </Badge>
                          {message.direction === 'outbound' && (
                            <Badge variant="outline">Enviada</Badge>
                          )}
                          {message.direction === 'inbound' && (
                            <Badge variant="outline">Recebida</Badge>
                          )}
                        </div>
                        <div className="text-right text-sm text-muted-foreground">
                          <p>{format(new Date(message.created_at), 'dd/MM/yy HH:mm', { locale: ptBR })}</p>
                          {message.patient_name && (
                            <p className="font-medium">{message.patient_name}</p>
                          )}
                        </div>
                      </div>
                      <p className="text-foreground leading-relaxed">{message.content}</p>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="received" className="space-y-4">
              {inboundMessages.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhuma mensagem recebida</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {inboundMessages.map((message) => (
                    <div key={message.id} className="p-4 rounded-lg border bg-card/50 mr-8">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getChannelIcon(message.channel)}</span>
                          <Badge className={getChannelColor(message.channel)}>
                            {message.channel.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="text-right text-sm text-muted-foreground">
                          <p>{format(new Date(message.created_at), 'dd/MM/yy HH:mm', { locale: ptBR })}</p>
                          {message.patient_name && (
                            <p className="font-medium">{message.patient_name}</p>
                          )}
                        </div>
                      </div>
                      <p className="text-foreground leading-relaxed">{message.content}</p>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="sent" className="space-y-4">
              {outboundMessages.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhuma mensagem enviada</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {outboundMessages.map((message) => (
                    <div key={message.id} className="p-4 rounded-lg border bg-primary/5 ml-8">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getChannelIcon(message.channel)}</span>
                          <Badge className={getChannelColor(message.channel)}>
                            {message.channel.toUpperCase()}
                          </Badge>
                          <Badge className={getStatusColor(message.status)}>
                            {message.status.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="text-right text-sm text-muted-foreground">
                          <p>{format(new Date(message.created_at), 'dd/MM/yy HH:mm', { locale: ptBR })}</p>
                          {message.patient_name && (
                            <p className="font-medium">{message.patient_name}</p>
                          )}
                        </div>
                      </div>
                      <p className="text-foreground leading-relaxed">{message.content}</p>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default MessagesPage;