"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Megaphone, Plus, Search, Edit, Trash2, Send, Users, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

type Campaign = {
  id: string;
  name: string;
  message: string;
  channel: 'whatsapp' | 'email' | 'sms';
  status: 'draft' | 'scheduled' | 'sent' | 'completed';
  scheduled_date?: string;
  target_audience: string;
  sent_count: number;
  created_at: string;
};

const CampaignsPage = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  const [campaignForm, setCampaignForm] = useState({
    name: '',
    message: '',
    channel: 'whatsapp' as const,
    target_audience: 'all',
    scheduled_date: ''
  });

  useEffect(() => {
    fetchCampaigns();
  }, []);

  useEffect(() => {
    const filtered = campaigns.filter(campaign =>
      campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      campaign.message.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredCampaigns(filtered);
  }, [campaigns, searchTerm]);

  const fetchCampaigns = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/campaigns');
      if (!response.ok) throw new Error('Failed to fetch campaigns');
      const data = await response.json();
      setCampaigns(Array.isArray(data) ? data : []);
    } catch (error) {
      setCampaigns([]);
      toast({
        title: "Erro ao carregar campanhas",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const method = editingCampaign ? 'PATCH' : 'POST';
      const url = editingCampaign ? `/api/campaigns/${editingCampaign.id}` : '/api/campaigns';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...campaignForm,
          status: 'draft',
          scheduled_date: campaignForm.scheduled_date || null
        })
      });

      if (!response.ok) throw new Error('Failed to save campaign');

      toast({
        title: editingCampaign ? "Campanha atualizada" : "Campanha criada",
        description: editingCampaign ? "A campanha foi atualizada com sucesso" : "Nova campanha criada com sucesso"
      });

      setDialogOpen(false);
      setEditingCampaign(null);
      setCampaignForm({
        name: '',
        message: '',
        channel: 'whatsapp',
        target_audience: 'all',
        scheduled_date: ''
      });
      
      fetchCampaigns();
    } catch (error) {
      toast({
        title: "Erro ao salvar campanha",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    }
  };

  const handleEdit = (campaign: Campaign) => {
    setEditingCampaign(campaign);
    setCampaignForm({
      name: campaign.name,
      message: campaign.message,
      channel: campaign.channel,
      target_audience: campaign.target_audience,
      scheduled_date: campaign.scheduled_date || ''
    });
    setDialogOpen(true);
  };

  const handleDelete = async (campaignId: string) => {
    if (!confirm('Tem certeza que deseja excluir esta campanha?')) return;

    try {
      const response = await fetch(`/api/campaigns/${campaignId}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete campaign');

      toast({
        title: "Campanha excluída",
        description: "A campanha foi removida com sucesso"
      });
      
      fetchCampaigns();
    } catch (error) {
      toast({
        title: "Erro ao excluir campanha",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    }
  };

  const sendCampaign = async (campaignId: string) => {
    try {
      const response = await fetch(`/api/campaigns/${campaignId}/send`, {
        method: 'POST'
      });

      if (!response.ok) throw new Error('Failed to send campaign');

      toast({
        title: "Campanha enviada",
        description: "A campanha foi enviada com sucesso"
      });
      
      fetchCampaigns();
    } catch (error) {
      toast({
        title: "Erro ao enviar campanha",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-success text-success-foreground';
      case 'sent': return 'bg-primary text-primary-foreground';
      case 'scheduled': return 'bg-warning text-warning-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp': return '📱';
      case 'email': return '📧';
      case 'sms': return '💬';
      default: return '📢';
    }
  };

  const openNewCampaignDialog = () => {
    setEditingCampaign(null);
    setCampaignForm({
      name: '',
      message: '',
      channel: 'whatsapp',
      target_audience: 'all',
      scheduled_date: ''
    });
    setDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Campanhas</h1>
          <p className="text-muted-foreground">Gerencie campanhas de marketing</p>
        </div>
        
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openNewCampaignDialog}>
              <Plus className="mr-2 h-4 w-4" />
              Nova Campanha
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>
                  {editingCampaign ? 'Editar Campanha' : 'Nova Campanha'}
                </DialogTitle>
                <DialogDescription>
                  {editingCampaign ? 'Atualize as informações da campanha' : 'Crie uma nova campanha de marketing'}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome da Campanha *</Label>
                  <Input
                    id="name"
                    value={campaignForm.name}
                    onChange={(e) => setCampaignForm({ ...campaignForm, name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="channel">Canal</Label>
                  <Select
                    value={campaignForm.channel}
                    onValueChange={(value: any) => setCampaignForm({ ...campaignForm, channel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="whatsapp">WhatsApp</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="target_audience">Público Alvo</Label>
                  <Select
                    value={campaignForm.target_audience}
                    onValueChange={(value) => setCampaignForm({ ...campaignForm, target_audience: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os pacientes</SelectItem>
                      <SelectItem value="active">Pacientes ativos</SelectItem>
                      <SelectItem value="inactive">Pacientes inativos</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="scheduled_date">Data de Envio (opcional)</Label>
                  <Input
                    id="scheduled_date"
                    type="datetime-local"
                    value={campaignForm.scheduled_date}
                    onChange={(e) => setCampaignForm({ ...campaignForm, scheduled_date: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Mensagem *</Label>
                  <Textarea
                    id="message"
                    value={campaignForm.message}
                    onChange={(e) => setCampaignForm({ ...campaignForm, message: e.target.value })}
                    rows={4}
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">
                  {editingCampaign ? 'Atualizar' : 'Criar'} Campanha
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Megaphone className="mr-2 h-5 w-5 text-primary" />
            Lista de Campanhas
          </CardTitle>
          <CardDescription>
            {filteredCampaigns.length} campanha(s) encontrada(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar campanhas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {filteredCampaigns.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Megaphone className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>Nenhuma campanha encontrada</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Canal</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Público</TableHead>
                  <TableHead>Enviados</TableHead>
                  <TableHead>Criado em</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCampaigns.map((campaign) => (
                  <TableRow key={campaign.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{campaign.name}</p>
                        <p className="text-sm text-muted-foreground truncate max-w-xs">
                          {campaign.message}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span className="mr-2">{getChannelIcon(campaign.channel)}</span>
                        {campaign.channel.charAt(0).toUpperCase() + campaign.channel.slice(1)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(campaign.status)}>
                        {campaign.status === 'draft' && 'Rascunho'}
                        {campaign.status === 'scheduled' && 'Agendado'}
                        {campaign.status === 'sent' && 'Enviado'}
                        {campaign.status === 'completed' && 'Concluído'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm">
                        <Users className="mr-1 h-3 w-3" />
                        {campaign.target_audience === 'all' && 'Todos'}
                        {campaign.target_audience === 'active' && 'Ativos'}
                        {campaign.target_audience === 'inactive' && 'Inativos'}
                      </div>
                    </TableCell>
                    <TableCell>{campaign.sent_count}</TableCell>
                    <TableCell>
                      {format(new Date(campaign.created_at), 'dd/MM/yyyy', { locale: ptBR })}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        {campaign.status === 'draft' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => sendCampaign(campaign.id)}
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(campaign)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(campaign.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignsPage;
