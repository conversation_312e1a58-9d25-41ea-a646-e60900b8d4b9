[{"name": "generate-buildid", "duration": 1237, "timestamp": 5827875688, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751850762967, "traceId": "3ce40036c9888f88"}, {"name": "load-custom-routes", "duration": 1406, "timestamp": 5827877524, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751850762969, "traceId": "3ce40036c9888f88"}, {"name": "create-dist-dir", "duration": 6938, "timestamp": 5828116431, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751850763208, "traceId": "3ce40036c9888f88"}, {"name": "create-pages-mapping", "duration": 522, "timestamp": 5828163810, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751850763255, "traceId": "3ce40036c9888f88"}, {"name": "collect-app-paths", "duration": 34438, "timestamp": 5828164418, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751850763256, "traceId": "3ce40036c9888f88"}, {"name": "create-app-mapping", "duration": 5705, "timestamp": 5828198943, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751850763290, "traceId": "3ce40036c9888f88"}, {"name": "public-dir-conflict-check", "duration": 5180, "timestamp": 5828207937, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751850763299, "traceId": "3ce40036c9888f88"}, {"name": "generate-routes-manifest", "duration": 12832, "timestamp": 5828214732, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751850763306, "traceId": "3ce40036c9888f88"}, {"name": "next-build", "duration": 7733266, "timestamp": 5827382803, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.5", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1751850762474, "traceId": "3ce40036c9888f88"}]