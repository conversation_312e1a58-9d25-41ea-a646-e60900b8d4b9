import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Appointment = Tables<'appointments'>
type AppointmentInsert = TablesInsert<'appointments'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const date = searchParams.get('date')

      let query = supabase
        .from('appointments')
        .select(`
          *,
          patients!inner(name)
        `)
        .eq('user_id', userId)

      if (date) {
        const startDate = new Date(date)
        startDate.setHours(0, 0, 0, 0)
        
        const endDate = new Date(startDate)
        endDate.setDate(endDate.getDate() + 1)
        
        query = query
          .gte('start_time', startDate.toISOString())
          .lt('start_time', endDate.toISOString())
      }

      const { data: appointments, error } = await query.order('start_time')

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(appointments)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const appointmentData: AppointmentInsert = {
        ...body,
        user_id: userId,
        status: body.status || 'scheduled',
        price: body.price ? parseFloat(body.price) : null,
        start_time: new Date(body.start_time).toISOString(),
        end_time: new Date(body.end_time).toISOString(),
      }

      const { data: appointment, error } = await supabase
        .from('appointments')
        .insert(appointmentData)
        .select(`
          *,
          patients!inner(name)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(appointment, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
