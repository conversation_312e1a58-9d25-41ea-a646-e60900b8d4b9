(()=>{var M;function j(G,J){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(G);J&&(Z=Z.filter(function(U){return Object.getOwnPropertyDescriptor(G,U).enumerable})),X.push.apply(X,Z)}return X}function R(G){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?j(Object(X),!0).forEach(function(Z){v(G,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):j(Object(X)).forEach(function(Z){Object.defineProperty(G,Z,Object.getOwnPropertyDescriptor(X,Z))})}return G}function v(G,J,X){if(J=F(J),J in G)Object.defineProperty(G,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[J]=X;return G}function F(G){var J=f(G,"string");return N(J)=="symbol"?J:String(J)}function f(G,J){if(N(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(G,J||"default");if(N(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(G)}function b(G,J){return g(G)||k(G,J)||_(G,J)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(G,J){if(!G)return;if(typeof G==="string")return W(G,J);var X=Object.prototype.toString.call(G).slice(8,-1);if(X==="Object"&&G.constructor)X=G.constructor.name;if(X==="Map"||X==="Set")return Array.from(G);if(X==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(X))return W(G,J)}function W(G,J){if(J==null||J>G.length)J=G.length;for(var X=0,Z=new Array(J);X<J;X++)Z[X]=G[X];return Z}function k(G,J){var X=G==null?null:typeof Symbol!="undefined"&&G[Symbol.iterator]||G["@@iterator"];if(X!=null){var Z,U,C,B,H=[],q=!0,Y=!1;try{if(C=(X=X.call(G)).next,J===0){if(Object(X)!==X)return;q=!1}else for(;!(q=(Z=C.call(X)).done)&&(H.push(Z.value),H.length!==J);q=!0);}catch(K){Y=!0,U=K}finally{try{if(!q&&X.return!=null&&(B=X.return(),Object(B)!==B))return}finally{if(Y)throw U}}return H}}function g(G){if(Array.isArray(G))return G}function N(G){return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},N(G)}var y=Object.defineProperty,fG=function G(J,X){for(var Z in X)y(J,Z,{get:X[Z],enumerable:!0,configurable:!0,set:function U(C){return X[Z]=function(){return C}}})};function A(G,J){if(G.one!==void 0&&J===1)return G.one;var X=J%10,Z=J%100;if(X===1&&Z!==11)return G.singularNominative.replace("{{count}}",String(J));else if(X>=2&&X<=4&&(Z<10||Z>20))return G.singularGenitive.replace("{{count}}",String(J));else return G.pluralGenitive.replace("{{count}}",String(J))}function Q(G){return function(J,X){if(X&&X.addSuffix)if(X.comparison&&X.comparison>0)if(G.future)return A(G.future,J);else return"\u043F\u0440\u0430\u0437 "+A(G.regular,J);else if(G.past)return A(G.past,J);else return A(G.regular,J)+" \u0442\u0430\u043C\u0443";else return A(G.regular,J)}}var m=function G(J,X){if(X&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\u043F\u0440\u0430\u0437 \u043F\u0430\u045E\u0445\u0432\u0456\u043B\u0456\u043D\u044B";else return"\u043F\u0430\u045E\u0445\u0432\u0456\u043B\u0456\u043D\u044B \u0442\u0430\u043C\u0443";return"\u043F\u0430\u045E\u0445\u0432\u0456\u043B\u0456\u043D\u044B"},c={lessThanXSeconds:Q({regular:{one:"\u043C\u0435\u043D\u0448 \u0437\u0430 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},future:{one:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),xSeconds:Q({regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0430",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443 \u0442\u0430\u043C\u0443",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B \u0442\u0430\u043C\u0443",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0442\u0430\u043C\u0443"},future:{singularNominative:"\u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),halfAMinute:m,lessThanXMinutes:Q({regular:{one:"\u043C\u0435\u043D\u0448 \u0437\u0430 \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularNominative:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D"},future:{one:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularNominative:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D"}}),xMinutes:Q({regular:{singularNominative:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0430",singularGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D"},past:{singularNominative:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443 \u0442\u0430\u043C\u0443",singularGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B \u0442\u0430\u043C\u0443",pluralGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D \u0442\u0430\u043C\u0443"},future:{singularNominative:"\u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D"}}),aboutXHours:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u0437\u0456\u043D",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u0437\u0456\u043D"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0443",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D"}}),xHours:Q({regular:{singularNominative:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0430",singularGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",pluralGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D"},past:{singularNominative:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0443 \u0442\u0430\u043C\u0443",singularGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B \u0442\u0430\u043C\u0443",pluralGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D \u0442\u0430\u043C\u0443"},future:{singularNominative:"\u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0443",singularGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",pluralGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D"}}),xDays:Q({regular:{singularNominative:"{{count}} \u0434\u0437\u0435\u043D\u044C",singularGenitive:"{{count}} \u0434\u043D\u0456",pluralGenitive:"{{count}} \u0434\u0437\u0451\u043D"}}),aboutXWeeks:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u0442\u044B\u0434\u043D\u0456",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0442\u044B\u0434\u043D\u044F\u045E",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0442\u044B\u0434\u043D\u044F\u045E"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0442\u044B\u0434\u0437\u0435\u043D\u044C",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0442\u044B\u0434\u043D\u0456",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0442\u044B\u0434\u043D\u044F\u045E"}}),xWeeks:Q({regular:{singularNominative:"{{count}} \u0442\u044B\u0434\u0437\u0435\u043D\u044C",singularGenitive:"{{count}} \u0442\u044B\u0434\u043D\u0456",pluralGenitive:"{{count}} \u0442\u044B\u0434\u043D\u044F\u045E"}}),aboutXMonths:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u043C\u0435\u0441\u044F\u0446\u0430",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u044B",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E"}}),xMonths:Q({regular:{singularNominative:"{{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u044B",pluralGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E"}}),aboutXYears:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u0433\u043E\u0434\u0430",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u043E\u045E",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u043E\u045E"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u043E\u045E"}}),xYears:Q({regular:{singularNominative:"{{count}} \u0433\u043E\u0434",singularGenitive:"{{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"{{count}} \u0433\u0430\u0434\u043E\u045E"}}),overXYears:Q({regular:{singularNominative:"\u0431\u043E\u043B\u044C\u0448 \u0437\u0430 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0431\u043E\u043B\u044C\u0448 \u0437\u0430 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448 \u0437\u0430 {{count}} \u0433\u0430\u0434\u043E\u045E"},future:{singularNominative:"\u0431\u043E\u043B\u044C\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0431\u043E\u043B\u044C\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u043E\u045E"}}),almostXYears:Q({regular:{singularNominative:"\u0430\u043C\u0430\u043B\u044C {{count}} \u0433\u043E\u0434",singularGenitive:"\u0430\u043C\u0430\u043B\u044C {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0430\u043C\u0430\u043B\u044C {{count}} \u0433\u0430\u0434\u043E\u045E"},future:{singularNominative:"\u0430\u043C\u0430\u043B\u044C \u043F\u0440\u0430\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0430\u043C\u0430\u043B\u044C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0430\u043C\u0430\u043B\u044C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u043E\u045E"}})},p=function G(J,X,Z){return Z=Z||{},c[J](X,Z)};function x(G){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):G.defaultWidth,Z=G.formats[X]||G.formats[G.defaultWidth];return Z}}var u={full:"EEEE, d MMMM y '\u0433.'",long:"d MMMM y '\u0433.'",medium:"d MMM y '\u0433.'",short:"dd.MM.y"},d={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},l={any:"{{date}}, {{time}}"},i={date:x({formats:u,defaultWidth:"full"}),time:x({formats:d,defaultWidth:"full"}),dateTime:x({formats:l,defaultWidth:"any"})},bG=7,s=365.2425,r=Math.pow(10,8)*24*60*60*1000,hG=-r,_G=604800000,kG=86400000,gG=60000,yG=3600000,mG=1000,cG=525600,pG=43200,uG=1440,dG=60,lG=3,iG=12,sG=4,n=3600,rG=60,O=n*24,nG=O*7,o=O*s,a=o/12,oG=a*3,T=Symbol.for("constructDateFrom");function P(G,J){if(typeof G==="function")return G(J);if(G&&N(G)==="object"&&T in G)return G[T](J);if(G instanceof Date)return new G.constructor(J);return new Date(J)}function t(G){for(var J=arguments.length,X=new Array(J>1?J-1:0),Z=1;Z<J;Z++)X[Z-1]=arguments[Z];var U=P.bind(null,G||X.find(function(C){return N(C)==="object"}));return X.map(U)}function e(){return z}function aG(G){z=G}var z={};function S(G,J){return P(J||G,G)}function w(G,J){var X,Z,U,C,B,H,q=e(),Y=(X=(Z=(U=(C=J===null||J===void 0?void 0:J.weekStartsOn)!==null&&C!==void 0?C:J===null||J===void 0||(B=J.locale)===null||B===void 0||(B=B.options)===null||B===void 0?void 0:B.weekStartsOn)!==null&&U!==void 0?U:q.weekStartsOn)!==null&&Z!==void 0?Z:(H=q.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&X!==void 0?X:0,K=S(G,J===null||J===void 0?void 0:J.in),E=K.getDay(),FG=(E<Y?7:0)+E-Y;return K.setDate(K.getDate()-FG),K.setHours(0,0,0,0),K}function L(G,J,X){var Z=t(X===null||X===void 0?void 0:X.in,G,J),U=b(Z,2),C=U[0],B=U[1];return+w(C,X)===+w(B,X)}function GG(G){var J=$[G];switch(G){case 0:case 3:case 5:case 6:return"'\u0443 \u043C\u0456\u043D\u0443\u043B\u0443\u044E "+J+" \u0430' p";case 1:case 2:case 4:return"'\u0443 \u043C\u0456\u043D\u0443\u043B\u044B "+J+" \u0430' p"}}function D(G){var J=$[G];return"'\u0443 "+J+" \u0430' p"}function JG(G){var J=$[G];switch(G){case 0:case 3:case 5:case 6:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0443\u044E "+J+" \u0430' p";case 1:case 2:case 4:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u044B "+J+" \u0430' p"}}var $=["\u043D\u044F\u0434\u0437\u0435\u043B\u044E","\u043F\u0430\u043D\u044F\u0434\u0437\u0435\u043B\u0430\u043A","\u0430\u045E\u0442\u043E\u0440\u0430\u043A","\u0441\u0435\u0440\u0430\u0434\u0443","\u0447\u0430\u0446\u044C\u0432\u0435\u0440","\u043F\u044F\u0442\u043D\u0456\u0446\u0443","\u0441\u0443\u0431\u043E\u0442\u0443"],XG=function G(J,X,Z){var U=S(J),C=U.getDay();if(L(U,X,Z))return D(C);else return GG(C)},ZG=function G(J,X,Z){var U=S(J),C=U.getDay();if(L(U,X,Z))return D(C);else return JG(C)},CG={lastWeek:XG,yesterday:"'\u0443\u0447\u043E\u0440\u0430 \u0430' p",today:"'\u0441\u0451\u043D\u044C\u043D\u044F \u0430' p",tomorrow:"'\u0437\u0430\u045E\u0442\u0440\u0430 \u0430' p",nextWeek:ZG,other:"P"},UG=function G(J,X,Z,U){var C=CG[J];if(typeof C==="function")return C(X,Z,U);return C};function V(G){return function(J,X){var Z=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",U;if(Z==="formatting"&&G.formattingValues){var C=G.defaultFormattingWidth||G.defaultWidth,B=X!==null&&X!==void 0&&X.width?String(X.width):C;U=G.formattingValues[B]||G.formattingValues[C]}else{var H=G.defaultWidth,q=X!==null&&X!==void 0&&X.width?String(X.width):G.defaultWidth;U=G.values[q]||G.values[H]}var Y=G.argumentCallback?G.argumentCallback(J):J;return U[Y]}}var BG={narrow:["\u0434\u0430 \u043D.\u044D.","\u043D.\u044D."],abbreviated:["\u0434\u0430 \u043D. \u044D.","\u043D. \u044D."],wide:["\u0434\u0430 \u043D\u0430\u0448\u0430\u0439 \u044D\u0440\u044B","\u043D\u0430\u0448\u0430\u0439 \u044D\u0440\u044B"]},HG={narrow:["1","2","3","4"],abbreviated:["1-\u044B \u043A\u0432.","2-\u0456 \u043A\u0432.","3-\u0456 \u043A\u0432.","4-\u044B \u043A\u0432."],wide:["1-\u044B \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2-\u0456 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3-\u0456 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4-\u044B \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},QG={narrow:["\u0421","\u041B","\u0421","\u041A","\u0422","\u0427","\u041B","\u0416","\u0412","\u041A","\u041B","\u0421"],abbreviated:["\u0441\u0442\u0443\u0434\u0437.","\u043B\u044E\u0442.","\u0441\u0430\u043A.","\u043A\u0440\u0430\u0441.","\u0442\u0440\u0430\u0432.","\u0447\u044D\u0440\u0432.","\u043B\u0456\u043F.","\u0436\u043D.","\u0432\u0435\u0440.","\u043A\u0430\u0441\u0442\u0440.","\u043B\u0456\u0441\u0442.","\u0441\u044C\u043D\u0435\u0436."],wide:["\u0441\u0442\u0443\u0434\u0437\u0435\u043D\u044C","\u043B\u044E\u0442\u044B","\u0441\u0430\u043A\u0430\u0432\u0456\u043A","\u043A\u0440\u0430\u0441\u0430\u0432\u0456\u043A","\u0442\u0440\u0430\u0432\u0435\u043D\u044C","\u0447\u044D\u0440\u0432\u0435\u043D\u044C","\u043B\u0456\u043F\u0435\u043D\u044C","\u0436\u043D\u0456\u0432\u0435\u043D\u044C","\u0432\u0435\u0440\u0430\u0441\u0435\u043D\u044C","\u043A\u0430\u0441\u0442\u0440\u044B\u0447\u043D\u0456\u043A","\u043B\u0456\u0441\u0442\u0430\u043F\u0430\u0434","\u0441\u044C\u043D\u0435\u0436\u0430\u043D\u044C"]},YG={narrow:["\u0421","\u041B","\u0421","\u041A","\u0422","\u0427","\u041B","\u0416","\u0412","\u041A","\u041B","\u0421"],abbreviated:["\u0441\u0442\u0443\u0434\u0437.","\u043B\u044E\u0442.","\u0441\u0430\u043A.","\u043A\u0440\u0430\u0441.","\u0442\u0440\u0430\u0432.","\u0447\u044D\u0440\u0432.","\u043B\u0456\u043F.","\u0436\u043D.","\u0432\u0435\u0440.","\u043A\u0430\u0441\u0442\u0440.","\u043B\u0456\u0441\u0442.","\u0441\u044C\u043D\u0435\u0436."],wide:["\u0441\u0442\u0443\u0434\u0437\u0435\u043D\u044F","\u043B\u044E\u0442\u0430\u0433\u0430","\u0441\u0430\u043A\u0430\u0432\u0456\u043A\u0430","\u043A\u0440\u0430\u0441\u0430\u0432\u0456\u043A\u0430","\u0442\u0440\u0430\u045E\u043D\u044F","\u0447\u044D\u0440\u0432\u0435\u043D\u044F","\u043B\u0456\u043F\u0435\u043D\u044F","\u0436\u043D\u0456\u045E\u043D\u044F","\u0432\u0435\u0440\u0430\u0441\u043D\u044F","\u043A\u0430\u0441\u0442\u0440\u044B\u0447\u043D\u0456\u043A\u0430","\u043B\u0456\u0441\u0442\u0430\u043F\u0430\u0434\u0430","\u0441\u044C\u043D\u0435\u0436\u043D\u044F"]},qG={narrow:["\u041D","\u041F","\u0410","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0434","\u043F\u043D","\u0430\u045E","\u0441\u0440","\u0447\u0446","\u043F\u0442","\u0441\u0431"],abbreviated:["\u043D\u044F\u0434\u0437","\u043F\u0430\u043D","\u0430\u045E\u0442","\u0441\u0435\u0440","\u0447\u0430\u0446\u044C","\u043F\u044F\u0442","\u0441\u0443\u0431"],wide:["\u043D\u044F\u0434\u0437\u0435\u043B\u044F","\u043F\u0430\u043D\u044F\u0434\u0437\u0435\u043B\u0430\u043A","\u0430\u045E\u0442\u043E\u0440\u0430\u043A","\u0441\u0435\u0440\u0430\u0434\u0430","\u0447\u0430\u0446\u044C\u0432\u0435\u0440","\u043F\u044F\u0442\u043D\u0456\u0446\u0430","\u0441\u0443\u0431\u043E\u0442\u0430"]},KG={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u0437\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u0437\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D\u0430\u0447",noon:"\u043F\u043E\u045E\u0434\u0437\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u0456\u0446\u0430",afternoon:"\u0434\u0437\u0435\u043D\u044C",evening:"\u0432\u0435\u0447\u0430\u0440",night:"\u043D\u043E\u0447"}},NG={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044B"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044B"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D\u0430\u0447",noon:"\u043F\u043E\u045E\u0434\u0437\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u0456\u0446\u044B",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447\u0430\u0440\u0430",night:"\u043D\u043E\u0447\u044B"}},EG=function G(J,X){var Z=String(X===null||X===void 0?void 0:X.unit),U=Number(J),C;if(Z==="date")C="-\u0433\u0430";else if(Z==="hour"||Z==="minute"||Z==="second")C="-\u044F";else C=(U%10===2||U%10===3)&&U%100!==12&&U%100!==13?"-\u0456":"-\u044B";return U+C},AG={ordinalNumber:EG,era:V({values:BG,defaultWidth:"wide"}),quarter:V({values:HG,defaultWidth:"wide",argumentCallback:function G(J){return J-1}}),month:V({values:QG,defaultWidth:"wide",formattingValues:YG,defaultFormattingWidth:"wide"}),day:V({values:qG,defaultWidth:"wide"}),dayPeriod:V({values:KG,defaultWidth:"any",formattingValues:NG,defaultFormattingWidth:"wide"})};function I(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=X.width,U=Z&&G.matchPatterns[Z]||G.matchPatterns[G.defaultMatchWidth],C=J.match(U);if(!C)return null;var B=C[0],H=Z&&G.parsePatterns[Z]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(H)?IG(H,function(E){return E.test(B)}):VG(H,function(E){return E.test(B)}),Y;Y=G.valueCallback?G.valueCallback(q):q,Y=X.valueCallback?X.valueCallback(Y):Y;var K=J.slice(B.length);return{value:Y,rest:K}}}function VG(G,J){for(var X in G)if(Object.prototype.hasOwnProperty.call(G,X)&&J(G[X]))return X;return}function IG(G,J){for(var X=0;X<G.length;X++)if(J(G[X]))return X;return}function RG(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=J.match(G.matchPattern);if(!Z)return null;var U=Z[0],C=J.match(G.parsePattern);if(!C)return null;var B=G.valueCallback?G.valueCallback(C[0]):C[0];B=X.valueCallback?X.valueCallback(B):B;var H=J.slice(U.length);return{value:B,rest:H}}}var MG=/^(\d+)(-?(е|я|га|і|ы|ае|ая|яя|шы|гі|ці|ты|мы))?/i,xG=/\d+/i,SG={narrow:/^((да )?н\.?\s?э\.?)/i,abbreviated:/^((да )?н\.?\s?э\.?)/i,wide:/^(да нашай эры|нашай эры|наша эра)/i},$G={any:[/^д/i,/^н/i]},jG={narrow:/^[1234]/i,abbreviated:/^[1234](-?[ыі]?)? кв.?/i,wide:/^[1234](-?[ыі]?)? квартал/i},WG={any:[/1/i,/2/i,/3/i,/4/i]},OG={narrow:/^[слкмчжв]/i,abbreviated:/^(студз|лют|сак|крас|тр(ав)?|чэрв|ліп|жн|вер|кастр|ліст|сьнеж)\.?/i,wide:/^(студзен[ья]|лют(ы|ага)|сакавіка?|красавіка?|тра(вень|ўня)|чэрвен[ья]|ліпен[ья]|жні(вень|ўня)|верас(ень|ня)|кастрычніка?|лістапада?|сьнеж(ань|ня))/i},TG={narrow:[/^с/i,/^л/i,/^с/i,/^к/i,/^т/i,/^ч/i,/^л/i,/^ж/i,/^в/i,/^к/i,/^л/i,/^с/i],any:[/^ст/i,/^лю/i,/^са/i,/^кр/i,/^тр/i,/^ч/i,/^ліп/i,/^ж/i,/^в/i,/^ка/i,/^ліс/i,/^сн/i]},PG={narrow:/^[нпасч]/i,short:/^(нд|ня|пн|па|аў|ат|ср|се|чц|ча|пт|пя|сб|су)\.?/i,abbreviated:/^(нядз?|ндз|пнд|пан|аўт|срд|сер|чцьв|чаць|птн|пят|суб).?/i,wide:/^(нядзел[яі]|панядзел(ак|ка)|аўтор(ак|ка)|серад[аы]|чацьв(ер|ярга)|пятніц[аы]|субот[аы])/i},zG={narrow:[/^н/i,/^п/i,/^а/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н/i,/^п[ан]/i,/^а/i,/^с[ер]/i,/^ч/i,/^п[ят]/i,/^с[уб]/i]},wG={narrow:/^([дп]п|поўн\.?|поўд\.?|ран\.?|дзень|дня|веч\.?|ночы?)/i,abbreviated:/^([дп]п|поўн\.?|поўд\.?|ран\.?|дзень|дня|веч\.?|ночы?)/i,wide:/^([дп]п|поўнач|поўдзень|раніц[аы]|дзень|дня|вечара?|ночы?)/i},LG={any:{am:/^дп/i,pm:/^пп/i,midnight:/^поўн/i,noon:/^поўд/i,morning:/^р/i,afternoon:/^д[зн]/i,evening:/^в/i,night:/^н/i}},DG={ordinalNumber:RG({matchPattern:MG,parsePattern:xG,valueCallback:function G(J){return parseInt(J,10)}}),era:I({matchPatterns:SG,defaultMatchWidth:"wide",parsePatterns:$G,defaultParseWidth:"any"}),quarter:I({matchPatterns:jG,defaultMatchWidth:"wide",parsePatterns:WG,defaultParseWidth:"any",valueCallback:function G(J){return J+1}}),month:I({matchPatterns:OG,defaultMatchWidth:"wide",parsePatterns:TG,defaultParseWidth:"any"}),day:I({matchPatterns:PG,defaultMatchWidth:"wide",parsePatterns:zG,defaultParseWidth:"any"}),dayPeriod:I({matchPatterns:wG,defaultMatchWidth:"wide",parsePatterns:LG,defaultParseWidth:"any"})},vG={code:"be-tarask",formatDistance:p,formatLong:i,formatRelative:UG,localize:AG,match:DG,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=R(R({},window.dateFns),{},{locale:R(R({},(M=window.dateFns)===null||M===void 0?void 0:M.locale),{},{beTarask:vG})})})();

//# debugId=16CF19F0E124D33364756E2164756E21
