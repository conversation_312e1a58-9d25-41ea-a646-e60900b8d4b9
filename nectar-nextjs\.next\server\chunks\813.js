"use strict";exports.id=813,exports.ids=[813],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>o});var a=r(43210);r(60687);var n=a.createContext(void 0);function o(e){let t=a.useContext(n);return e||t||"ltr"}},44493:(e,t,r)=>{r.d(t,{BT:()=>d,Wu:()=>u,ZB:()=>l,Zp:()=>i,aR:()=>s});var a=r(60687),n=r(43210),o=r(4780);let i=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let s=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));s.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let u=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent",n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},54300:(e,t,r)=>{r.d(t,{J:()=>u});var a=r(60687),n=r(43210),o=r(14163),i=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var s=r(24224),l=r(4780);let d=(0,s.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(i,{ref:r,className:(0,l.cn)(d(),e),...t}));u.displayName=i.displayName},56770:(e,t,r)=>{r.d(t,{tU:()=>q,av:()=>W,j7:()=>J,Xi:()=>O});var a=r(60687),n=r(43210),o=r(70569),i=r(11273),s=r(9510),l=r(98599),d=r(96963),u=r(14163),c=r(13495),f=r(65551),m=r(43),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[g,x,w]=(0,s.N)(b),[y,h]=(0,i.A)(b,[w]),[j,N]=y(b),R=n.forwardRef((e,t)=>(0,a.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(C,{...e,ref:t})})}));R.displayName=b;var C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:d,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:y,onEntryFocus:h,preventScrollOnEntryFocus:N=!1,...R}=e,C=n.useRef(null),D=(0,l.s)(t,C),F=(0,m.jH)(d),[I,A]=(0,f.i)({prop:g,defaultProp:w??null,onChange:y,caller:b}),[E,G]=n.useState(!1),k=(0,c.c)(h),K=x(r),M=n.useRef(!1),[S,L]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(p,k),()=>e.removeEventListener(p,k)},[k]),(0,a.jsx)(j,{scope:r,orientation:i,dir:F,loop:s,currentTabStopId:I,onItemFocus:n.useCallback(e=>A(e),[A]),onItemShiftTab:n.useCallback(()=>G(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,a.jsx)(u.sG.div,{tabIndex:E||0===S?-1:0,"data-orientation":i,...R,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=K().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),N)}}M.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>G(!1))})})}),D="RovingFocusGroupItem",F=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:s=!1,tabStopId:l,children:c,...f}=e,m=(0,d.B)(),p=l||m,v=N(D,r),b=v.currentTabStopId===p,w=x(r),{onFocusableItemAdd:y,onFocusableItemRemove:h,currentTabStopId:j}=v;return n.useEffect(()=>{if(i)return y(),()=>h()},[i,y,h]),(0,a.jsx)(g.ItemSlot,{scope:r,id:p,focusable:i,active:s,children:(0,a.jsx)(u.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return I[n]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>T(r))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=j}):c})})});F.displayName=D;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e,t=!1){let r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var A=r(46059),E="Tabs",[G,k]=(0,i.A)(E,[h]),K=h(),[M,S]=G(E),L=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:c="automatic",...p}=e,v=(0,m.jH)(l),[b,g]=(0,f.i)({prop:n,onChange:o,defaultProp:i??"",caller:E});return(0,a.jsx)(M,{scope:r,baseId:(0,d.B)(),value:b,onValueChange:g,orientation:s,dir:v,activationMode:c,children:(0,a.jsx)(u.sG.div,{dir:v,"data-orientation":s,...p,ref:t})})});L.displayName=E;var B="TabsList",P=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,i=S(B,r),s=K(r);return(0,a.jsx)(R,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:n,children:(0,a.jsx)(u.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});P.displayName=B;var H="TabsTrigger",U=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...s}=e,l=S(H,r),d=K(r),c=_(l.baseId,n),f=z(l.baseId,n),m=n===l.value;return(0,a.jsx)(F,{asChild:!0,...d,focusable:!i,active:m,children:(0,a.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":f,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||i||!e||l.onValueChange(n)})})})});U.displayName=H;var V="TabsContent",$=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:s,...l}=e,d=S(V,r),c=_(d.baseId,o),f=z(d.baseId,o),m=o===d.value,p=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(A.C,{present:i||m,children:({present:r})=>(0,a.jsx)(u.sG.div,{"data-state":m?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&s})})});function _(e,t){return`${e}-trigger-${t}`}function z(e,t){return`${e}-content-${t}`}$.displayName=V;var Z=r(4780);let q=L,J=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(P,{ref:r,className:(0,Z.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));J.displayName=P.displayName;let O=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(U,{ref:r,className:(0,Z.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));O.displayName=U.displayName;let W=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)($,{ref:r,className:(0,Z.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));W.displayName=$.displayName}};