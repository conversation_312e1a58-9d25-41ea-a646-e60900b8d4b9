(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(5155),s=t(2115),n=t(9708),o=t(2085),i=t(9434);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",hero:"bg-gradient-primary text-primary-foreground hover:shadow-medium transition-all duration-300 hover:scale-105",success:"bg-success text-success-foreground hover:bg-success/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:o,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(l({variant:s,size:o,className:t})),ref:r,...c})});d.displayName="Button"},1374:(e,r,t)=>{Promise.resolve().then(t.bind(t,2293))},2293:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>ed});var a=t(5155),s=t(2115),n=t(5695),o=t(285),i=t(6695),l=t(2523),d=t(3655),c=s.forwardRef((e,r)=>(0,a.jsx)(d.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));c.displayName="Label";var u=t(2085),f=t(9434);let m=(0,u.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),p=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(c,{ref:r,className:(0,f.cn)(m(),t),...s})});p.displayName=c.displayName;var v=t(5185),h=t(6081),g=t(7328),x=t(6101),b=t(1285),y=t(9033),w=t(5845),j=s.createContext(void 0);function N(e){let r=s.useContext(j);return e||r||"ltr"}var S="rovingFocusGroup.onEntryFocus",T={bubbles:!1,cancelable:!0},C="RovingFocusGroup",[I,E,R]=(0,g.N)(C),[A,D]=(0,h.A)(C,[R]),[F,k]=A(C),O=s.forwardRef((e,r)=>(0,a.jsx)(I.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(I.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(_,{...e,ref:r})})}));O.displayName=C;var _=s.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:n,loop:o=!1,dir:i,currentTabStopId:l,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:u,onEntryFocus:f,preventScrollOnEntryFocus:m=!1,...p}=e,h=s.useRef(null),g=(0,x.s)(r,h),b=N(i),[j,I]=(0,w.i)({prop:l,defaultProp:null!=c?c:null,onChange:u,caller:C}),[R,A]=s.useState(!1),D=(0,y.c)(f),k=E(t),O=s.useRef(!1),[_,M]=s.useState(0);return s.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(S,D),()=>e.removeEventListener(S,D)},[D]),(0,a.jsx)(F,{scope:t,orientation:n,dir:b,loop:o,currentTabStopId:j,onItemFocus:s.useCallback(e=>I(e),[I]),onItemShiftTab:s.useCallback(()=>A(!0),[]),onFocusableItemAdd:s.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>M(e=>e-1),[]),children:(0,a.jsx)(d.sG.div,{tabIndex:R||0===_?-1:0,"data-orientation":n,...p,ref:g,style:{outline:"none",...e.style},onMouseDown:(0,v.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,v.m)(e.onFocus,e=>{let r=!O.current;if(e.target===e.currentTarget&&r&&!R){let r=new CustomEvent(S,T);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=k().filter(e=>e.focusable);B([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),m)}}O.current=!1}),onBlur:(0,v.m)(e.onBlur,()=>A(!1))})})}),M="RovingFocusGroupItem",P=s.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:n=!0,active:o=!1,tabStopId:i,children:l,...c}=e,u=(0,b.B)(),f=i||u,m=k(M,t),p=m.currentTabStopId===f,h=E(t),{onFocusableItemAdd:g,onFocusableItemRemove:x,currentTabStopId:y}=m;return s.useEffect(()=>{if(n)return g(),()=>x()},[n,g,x]),(0,a.jsx)(I.ItemSlot,{scope:t,id:f,focusable:n,active:o,children:(0,a.jsx)(d.sG.span,{tabIndex:p?0:-1,"data-orientation":m.orientation,...c,ref:r,onMouseDown:(0,v.m)(e.onMouseDown,e=>{n?m.onItemFocus(f):e.preventDefault()}),onFocus:(0,v.m)(e.onFocus,()=>m.onItemFocus(f)),onKeyDown:(0,v.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var a;let s=(a=e.key,"rtl"!==t?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(s)))return z[s]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let a=t.indexOf(e.currentTarget);t=m.loop?function(e,r){return e.map((t,a)=>e[(r+a)%e.length])}(t,a+1):t.slice(a+1)}setTimeout(()=>B(t))}}),children:"function"==typeof l?l({isCurrentTabStop:p,hasTabStop:null!=y}):l})})});P.displayName=M;var z={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function B(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let a of e)if(a===t||(a.focus({preventScroll:r}),document.activeElement!==t))return}var G=t(8905),V="Tabs",[J,Z]=(0,h.A)(V,[D]),K=D(),[L,X]=J(V),q=s.forwardRef((e,r)=>{let{__scopeTabs:t,value:s,onValueChange:n,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:c="automatic",...u}=e,f=N(l),[m,p]=(0,w.i)({prop:s,onChange:n,defaultProp:null!=o?o:"",caller:V});return(0,a.jsx)(L,{scope:t,baseId:(0,b.B)(),value:m,onValueChange:p,orientation:i,dir:f,activationMode:c,children:(0,a.jsx)(d.sG.div,{dir:f,"data-orientation":i,...u,ref:r})})});q.displayName=V;var U="TabsList",Q=s.forwardRef((e,r)=>{let{__scopeTabs:t,loop:s=!0,...n}=e,o=X(U,t),i=K(t);return(0,a.jsx)(O,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:s,children:(0,a.jsx)(d.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:r})})});Q.displayName=U;var W="TabsTrigger",$=s.forwardRef((e,r)=>{let{__scopeTabs:t,value:s,disabled:n=!1,...o}=e,i=X(W,t),l=K(t),c=ee(i.baseId,s),u=er(i.baseId,s),f=s===i.value;return(0,a.jsx)(P,{asChild:!0,...l,focusable:!n,active:f,children:(0,a.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:c,...o,ref:r,onMouseDown:(0,v.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(s)}),onKeyDown:(0,v.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(s)}),onFocus:(0,v.m)(e.onFocus,()=>{let e="manual"!==i.activationMode;f||n||!e||i.onValueChange(s)})})})});$.displayName=W;var H="TabsContent",Y=s.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,forceMount:o,children:i,...l}=e,c=X(H,t),u=ee(c.baseId,n),f=er(c.baseId,n),m=n===c.value,p=s.useRef(m);return s.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(G.C,{present:o||m,children:t=>{let{present:s}=t;return(0,a.jsx)(d.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:f,tabIndex:0,...l,ref:r,style:{...e.style,animationDuration:p.current?"0s":void 0},children:s&&i})}})});function ee(e,r){return"".concat(e,"-trigger-").concat(r)}function er(e,r){return"".concat(e,"-content-").concat(r)}Y.displayName=H;let et=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(Q,{ref:r,className:(0,f.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s})});et.displayName=Q.displayName;let ea=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)($,{ref:r,className:(0,f.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...s})});ea.displayName=$.displayName;let es=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(Y,{ref:r,className:(0,f.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...s})});es.displayName=Y.displayName;var en=t(4771),eo=t(1976),ei=t(6874),el=t.n(ei);function ed(){let[e,r]=(0,s.useState)(!1),{signIn:t,signUp:d}=(0,en.A)(),c=(0,n.useRouter)(),[u,f]=(0,s.useState)({email:"",password:""}),[m,v]=(0,s.useState)({name:"",email:"",password:"",confirmPassword:""}),h=async e=>{e.preventDefault(),r(!0);let{error:a}=await t(u.email,u.password);a||c.push("/dashboard"),r(!1)},g=async e=>{if(e.preventDefault(),m.password!==m.confirmPassword)return;r(!0);let{error:t}=await d(m.email,m.password,m.name);t||v({name:"",email:"",password:"",confirmPassword:""}),r(!1)};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(el(),{href:"/",className:"inline-flex items-center space-x-2",children:[(0,a.jsx)(eo.A,{className:"h-8 w-8 text-primary"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-foreground",children:"Nectar Sa\xfade"})]})}),(0,a.jsxs)(q,{defaultValue:"login",className:"w-full",children:[(0,a.jsxs)(et,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(ea,{value:"login",children:"Entrar"}),(0,a.jsx)(ea,{value:"register",children:"Cadastrar"})]}),(0,a.jsx)(es,{value:"login",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Entrar na sua conta"}),(0,a.jsx)(i.BT,{children:"Digite suas credenciais para acessar o sistema"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p,{htmlFor:"email",children:"Email"}),(0,a.jsx)(l.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:u.email,onChange:e=>f({...u,email:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p,{htmlFor:"password",children:"Senha"}),(0,a.jsx)(l.p,{id:"password",type:"password",value:u.password,onChange:e=>f({...u,password:e.target.value}),required:!0})]}),(0,a.jsx)(o.$,{type:"submit",className:"w-full",disabled:e,children:e?"Entrando...":"Entrar"})]})})]})}),(0,a.jsx)(es,{value:"register",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Criar nova conta"}),(0,a.jsx)(i.BT,{children:"Preencha os dados para criar sua conta"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p,{htmlFor:"name",children:"Nome completo"}),(0,a.jsx)(l.p,{id:"name",type:"text",placeholder:"Seu nome completo",value:m.name,onChange:e=>v({...m,name:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p,{htmlFor:"register-email",children:"Email"}),(0,a.jsx)(l.p,{id:"register-email",type:"email",placeholder:"<EMAIL>",value:m.email,onChange:e=>v({...m,email:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p,{htmlFor:"register-password",children:"Senha"}),(0,a.jsx)(l.p,{id:"register-password",type:"password",value:m.password,onChange:e=>v({...m,password:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p,{htmlFor:"confirm-password",children:"Confirmar senha"}),(0,a.jsx)(l.p,{id:"confirm-password",type:"password",value:m.confirmPassword,onChange:e=>v({...m,confirmPassword:e.target.value}),required:!0})]}),(0,a.jsx)(o.$,{type:"submit",className:"w-full",disabled:e,children:e?"Criando conta...":"Criar conta"})]})})]})})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(el(),{href:"/",className:"text-sm text-muted-foreground hover:text-foreground",children:"← Voltar para o in\xedcio"})})]})})}},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(5155),s=t(2115),n=t(9434);let o=s.forwardRef((e,r)=>{let{className:t,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...o})});o.displayName="Input"},4771:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var a=t(2115),s=t(3865),n=t(7481);function o(){let[e,r]=(0,a.useState)(null),[t,o]=(0,a.useState)(null),[i,l]=(0,a.useState)(!0),{toast:d}=(0,n.dj)();return(0,a.useEffect)(()=>{let e=(0,s.createBrowserClient)("https://zmwdnemlzndjavlriyrc.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM"),{data:{subscription:t}}=e.auth.onAuthStateChange((e,t)=>{var a;o(t),r(null!=(a=null==t?void 0:t.user)?a:null),l(!1)});return e.auth.getSession().then(e=>{var t;let{data:{session:a}}=e;o(a),r(null!=(t=null==a?void 0:a.user)?t:null),l(!1)}),()=>t.unsubscribe()},[]),{user:e,session:t,loading:i,signIn:async(e,r)=>{try{let t=await fetch("/api/auth/signin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})}),a=await t.json();if(!t.ok)return d({title:"Erro no login",description:a.error,variant:"destructive"}),{error:a.error};return{error:null}}catch(e){return d({title:"Erro no login",description:"Erro inesperado ao fazer login",variant:"destructive"}),{error:e}}},signUp:async(e,r,t)=>{try{let a=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r,name:t})}),s=await a.json();if(!a.ok)return d({title:"Erro no cadastro",description:s.error,variant:"destructive"}),{error:s.error};return d({title:"Cadastro realizado",description:s.message||"Verifique seu email para confirmar a conta"}),{error:null}}catch(e){return d({title:"Erro no cadastro",description:"Erro inesperado ao fazer cadastro",variant:"destructive"}),{error:e}}},signOut:async()=>{try{await fetch("/api/auth/signout",{method:"POST"}),d({title:"Logout realizado",description:"Voc\xea foi desconectado com sucesso"})}catch(e){console.error("Error signing out:",e)}}}}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i});var a=t(5155),s=t(2115),n=t(9434);let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});o.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});l.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...s})}).displayName="CardFooter"},7481:(e,r,t)=>{"use strict";t.d(r,{dj:()=>f});var a=t(2115);let s=0,n=new Map,o=e=>{if(n.has(e))return;let r=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,r)},i=(e,r)=>{switch(r.type){case"ADD_TOAST":return{...e,toasts:[r.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===r.toast.id?{...e,...r.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=r;return t?o(t):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===r.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==r.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function u(e){let{...r}=e,t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...r,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,r]=a.useState(d);return a.useEffect(()=>(l.push(r),()=>{let e=l.indexOf(r);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var a=t(2596),s=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[455,801,573,328,441,684,358],()=>r(1374)),_N_E=e.O()}]);