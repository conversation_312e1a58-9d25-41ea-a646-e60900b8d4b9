[{"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\[id]\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signin\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signout\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signup\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\campaigns\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\dashboard\\stats\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\messages\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\[id]\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\auth\\page.tsx": "11", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx": "13", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\AppSidebar.tsx": "15", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\DashboardLayout.tsx": "16", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\LandingPage.tsx": "17", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\Providers.tsx": "18", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ReactQueryProvider.tsx": "19", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\accordion.tsx": "20", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert-dialog.tsx": "21", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert.tsx": "22", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\aspect-ratio.tsx": "23", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\avatar.tsx": "24", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\badge.tsx": "25", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\breadcrumb.tsx": "26", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\button.tsx": "27", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\calendar.tsx": "28", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\card.tsx": "29", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\carousel.tsx": "30", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\chart.tsx": "31", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\checkbox.tsx": "32", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\collapsible.tsx": "33", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\command.tsx": "34", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\context-menu.tsx": "35", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dialog.tsx": "36", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\drawer.tsx": "37", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dropdown-menu.tsx": "38", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\form.tsx": "39", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\hover-card.tsx": "40", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input-otp.tsx": "41", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input.tsx": "42", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\label.tsx": "43", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\menubar.tsx": "44", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\navigation-menu.tsx": "45", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\pagination.tsx": "46", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\popover.tsx": "47", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\progress.tsx": "48", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\radio-group.tsx": "49", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\resizable.tsx": "50", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\scroll-area.tsx": "51", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\select.tsx": "52", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\separator.tsx": "53", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sheet.tsx": "54", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sidebar.tsx": "55", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\skeleton.tsx": "56", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\slider.tsx": "57", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sonner.tsx": "58", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\switch.tsx": "59", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\table.tsx": "60", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tabs.tsx": "61", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\textarea.tsx": "62", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toast.tsx": "63", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toaster.tsx": "64", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle-group.tsx": "65", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle.tsx": "66", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tooltip.tsx": "67", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\use-toast.ts": "68", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-mobile.tsx": "69", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-toast.ts": "70", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAuth.ts": "71", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\api-utils.ts": "72", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\client.ts": "73", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\server.ts": "74", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\utils.ts": "75", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\middleware.ts": "76", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\types\\supabase.ts": "77"}, {"size": 2152, "mtime": 1751834979319, "results": "78", "hashOfConfig": "79"}, {"size": 2475, "mtime": 1751834992702, "results": "80", "hashOfConfig": "79"}, {"size": 779, "mtime": 1751835044056, "results": "81", "hashOfConfig": "79"}, {"size": 543, "mtime": 1751835056838, "results": "82", "hashOfConfig": "79"}, {"size": 928, "mtime": 1751835050748, "results": "83", "hashOfConfig": "79"}, {"size": 1435, "mtime": 1751835016451, "results": "84", "hashOfConfig": "79"}, {"size": 2044, "mtime": 1751841287549, "results": "85", "hashOfConfig": "79"}, {"size": 1919, "mtime": 1751835004779, "results": "86", "hashOfConfig": "79"}, {"size": 1354, "mtime": 1751834954277, "results": "87", "hashOfConfig": "79"}, {"size": 2085, "mtime": 1751834967142, "results": "88", "hashOfConfig": "79"}, {"size": 6844, "mtime": 1751836670752, "results": "89", "hashOfConfig": "79"}, {"size": 10528, "mtime": 1751836728647, "results": "90", "hashOfConfig": "79"}, {"size": 784, "mtime": 1751837997834, "results": "91", "hashOfConfig": "79"}, {"size": 114, "mtime": 1751836641020, "results": "92", "hashOfConfig": "79"}, {"size": 2474, "mtime": 1751836490671, "results": "93", "hashOfConfig": "79"}, {"size": 1439, "mtime": 1751836504981, "results": "94", "hashOfConfig": "79"}, {"size": 13449, "mtime": 1751836560647, "results": "95", "hashOfConfig": "79"}, {"size": 504, "mtime": 1751838010463, "results": "96", "hashOfConfig": "79"}, {"size": 494, "mtime": 1751836596110, "results": "97", "hashOfConfig": "79"}, {"size": 2033, "mtime": 1751818663653, "results": "98", "hashOfConfig": "79"}, {"size": 4559, "mtime": 1751818663655, "results": "99", "hashOfConfig": "79"}, {"size": 1643, "mtime": 1751818663655, "results": "100", "hashOfConfig": "79"}, {"size": 145, "mtime": 1751818663657, "results": "101", "hashOfConfig": "79"}, {"size": 1453, "mtime": 1751818663657, "results": "102", "hashOfConfig": "79"}, {"size": 1164, "mtime": 1751818663657, "results": "103", "hashOfConfig": "79"}, {"size": 2816, "mtime": 1751818663659, "results": "104", "hashOfConfig": "79"}, {"size": 2235, "mtime": 1751818663659, "results": "105", "hashOfConfig": "79"}, {"size": 2684, "mtime": 1751818663659, "results": "106", "hashOfConfig": "79"}, {"size": 1956, "mtime": 1751818663659, "results": "107", "hashOfConfig": "79"}, {"size": 6470, "mtime": 1751818663659, "results": "108", "hashOfConfig": "79"}, {"size": 10829, "mtime": 1751818663659, "results": "109", "hashOfConfig": "79"}, {"size": 1084, "mtime": 1751818663665, "results": "110", "hashOfConfig": "79"}, {"size": 324, "mtime": 1751818663665, "results": "111", "hashOfConfig": "79"}, {"size": 5032, "mtime": 1751818663666, "results": "112", "hashOfConfig": "79"}, {"size": 7444, "mtime": 1751818663667, "results": "113", "hashOfConfig": "79"}, {"size": 3955, "mtime": 1751818663667, "results": "114", "hashOfConfig": "79"}, {"size": 3123, "mtime": 1751818663669, "results": "115", "hashOfConfig": "79"}, {"size": 7493, "mtime": 1751818663669, "results": "116", "hashOfConfig": "79"}, {"size": 4261, "mtime": 1751818663669, "results": "117", "hashOfConfig": "79"}, {"size": 1211, "mtime": 1751818663669, "results": "118", "hashOfConfig": "79"}, {"size": 2223, "mtime": 1751818663672, "results": "119", "hashOfConfig": "79"}, {"size": 813, "mtime": 1751818663673, "results": "120", "hashOfConfig": "79"}, {"size": 734, "mtime": 1751818663674, "results": "121", "hashOfConfig": "79"}, {"size": 8208, "mtime": 1751818663674, "results": "122", "hashOfConfig": "79"}, {"size": 5174, "mtime": 1751818663674, "results": "123", "hashOfConfig": "79"}, {"size": 2868, "mtime": 1751818663674, "results": "124", "hashOfConfig": "79"}, {"size": 1259, "mtime": 1751818663678, "results": "125", "hashOfConfig": "79"}, {"size": 803, "mtime": 1751818663678, "results": "126", "hashOfConfig": "79"}, {"size": 1509, "mtime": 1751818663678, "results": "127", "hashOfConfig": "79"}, {"size": 1752, "mtime": 1751818663678, "results": "128", "hashOfConfig": "79"}, {"size": 1688, "mtime": 1751818663678, "results": "129", "hashOfConfig": "79"}, {"size": 5773, "mtime": 1751818663681, "results": "130", "hashOfConfig": "79"}, {"size": 785, "mtime": 1751818663682, "results": "131", "hashOfConfig": "79"}, {"size": 4381, "mtime": 1751818663683, "results": "132", "hashOfConfig": "79"}, {"size": 24128, "mtime": 1751818663683, "results": "133", "hashOfConfig": "79"}, {"size": 276, "mtime": 1751818663685, "results": "134", "hashOfConfig": "79"}, {"size": 1103, "mtime": 1751818663685, "results": "135", "hashOfConfig": "79"}, {"size": 923, "mtime": 1751818663685, "results": "136", "hashOfConfig": "79"}, {"size": 1166, "mtime": 1751818663688, "results": "137", "hashOfConfig": "79"}, {"size": 2882, "mtime": 1751818663689, "results": "138", "hashOfConfig": "79"}, {"size": 1936, "mtime": 1751818663690, "results": "139", "hashOfConfig": "79"}, {"size": 796, "mtime": 1751818663691, "results": "140", "hashOfConfig": "79"}, {"size": 4972, "mtime": 1751818663693, "results": "141", "hashOfConfig": "79"}, {"size": 805, "mtime": 1751818663694, "results": "142", "hashOfConfig": "79"}, {"size": 1798, "mtime": 1751818663695, "results": "143", "hashOfConfig": "79"}, {"size": 1478, "mtime": 1751818663697, "results": "144", "hashOfConfig": "79"}, {"size": 1173, "mtime": 1751818663698, "results": "145", "hashOfConfig": "79"}, {"size": 85, "mtime": 1751818663699, "results": "146", "hashOfConfig": "79"}, {"size": 584, "mtime": 1751818663699, "results": "147", "hashOfConfig": "79"}, {"size": 4086, "mtime": 1751818663699, "results": "148", "hashOfConfig": "79"}, {"size": 3086, "mtime": 1751836462755, "results": "149", "hashOfConfig": "79"}, {"size": 1534, "mtime": 1751834943747, "results": "150", "hashOfConfig": "79"}, {"size": 271, "mtime": 1751834858396, "results": "151", "hashOfConfig": "79"}, {"size": 849, "mtime": 1751834866579, "results": "152", "hashOfConfig": "79"}, {"size": 166, "mtime": 1751835083516, "results": "153", "hashOfConfig": "79"}, {"size": 2699, "mtime": 1751834915265, "results": "154", "hashOfConfig": "79"}, {"size": 9033, "mtime": 1751834850497, "results": "155", "hashOfConfig": "79"}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "18sgt8c", {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\route.ts", ["387"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\[id]\\route.ts", ["388"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signin\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signout\\route.ts", ["389"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signup\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\campaigns\\route.ts", ["390"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\dashboard\\stats\\route.ts", ["391"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\messages\\route.ts", ["392"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\route.ts", ["393"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\[id]\\route.ts", ["394"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\auth\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\AppSidebar.tsx", ["395", "396"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\LandingPage.tsx", ["397", "398", "399", "400", "401"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\Providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ReactQueryProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\calendar.tsx", ["402", "403"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\chart.tsx", ["404"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\command.tsx", ["405"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\textarea.tsx", ["406"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\use-toast.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-mobile.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-toast.ts", ["407"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAuth.ts", ["408", "409"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\api-utils.ts", ["410", "411", "412"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\middleware.ts", ["413"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\types\\supabase.ts", [], [], {"ruleId": "414", "severity": 2, "message": "415", "line": 5, "column": 6, "nodeType": null, "messageId": "416", "endLine": 5, "endColumn": 17}, {"ruleId": "414", "severity": 2, "message": "415", "line": 5, "column": 6, "nodeType": null, "messageId": "416", "endLine": 5, "endColumn": 17}, {"ruleId": "414", "severity": 2, "message": "417", "line": 5, "column": 28, "nodeType": null, "messageId": "416", "endLine": 5, "endColumn": 35}, {"ruleId": "414", "severity": 2, "message": "418", "line": 5, "column": 6, "nodeType": null, "messageId": "416", "endLine": 5, "endColumn": 14}, {"ruleId": "419", "severity": 2, "message": "420", "line": 46, "column": 58, "nodeType": "421", "messageId": "422", "endLine": 46, "endColumn": 61, "suggestions": "423"}, {"ruleId": "414", "severity": 2, "message": "424", "line": 5, "column": 6, "nodeType": null, "messageId": "416", "endLine": 5, "endColumn": 13}, {"ruleId": "414", "severity": 2, "message": "425", "line": 5, "column": 6, "nodeType": null, "messageId": "416", "endLine": 5, "endColumn": 13}, {"ruleId": "414", "severity": 2, "message": "425", "line": 5, "column": 6, "nodeType": null, "messageId": "416", "endLine": 5, "endColumn": 13}, {"ruleId": "414", "severity": 2, "message": "426", "line": 3, "column": 10, "nodeType": null, "messageId": "416", "endLine": 3, "endColumn": 18}, {"ruleId": "414", "severity": 2, "message": "427", "line": 15, "column": 3, "nodeType": null, "messageId": "416", "endLine": 15, "endColumn": 17}, {"ruleId": "414", "severity": 2, "message": "426", "line": 3, "column": 10, "nodeType": null, "messageId": "416", "endLine": 3, "endColumn": 18}, {"ruleId": "428", "severity": 2, "message": "429", "line": 209, "column": 23, "nodeType": "430", "messageId": "431", "suggestions": "432"}, {"ruleId": "428", "severity": 2, "message": "429", "line": 209, "column": 93, "nodeType": "430", "messageId": "431", "suggestions": "433"}, {"ruleId": "428", "severity": 2, "message": "429", "line": 224, "column": 23, "nodeType": "430", "messageId": "431", "suggestions": "434"}, {"ruleId": "428", "severity": 2, "message": "429", "line": 224, "column": 95, "nodeType": "430", "messageId": "431", "suggestions": "435"}, {"ruleId": "414", "severity": 2, "message": "436", "line": 55, "column": 25, "nodeType": null, "messageId": "416", "endLine": 55, "endColumn": 31}, {"ruleId": "414", "severity": 2, "message": "436", "line": 56, "column": 26, "nodeType": null, "messageId": "416", "endLine": 56, "endColumn": 32}, {"ruleId": "414", "severity": 2, "message": "437", "line": 70, "column": 7, "nodeType": null, "messageId": "416", "endLine": 70, "endColumn": 8}, {"ruleId": "438", "severity": 2, "message": "439", "line": 24, "column": 11, "nodeType": "440", "messageId": "441", "endLine": 24, "endColumn": 29, "suggestions": "442"}, {"ruleId": "438", "severity": 2, "message": "439", "line": 5, "column": 18, "nodeType": "440", "messageId": "441", "endLine": 5, "endColumn": 31, "suggestions": "443"}, {"ruleId": "414", "severity": 2, "message": "444", "line": 18, "column": 7, "nodeType": null, "messageId": "445", "endLine": 18, "endColumn": 18}, {"ruleId": "419", "severity": 2, "message": "420", "line": 56, "column": 21, "nodeType": "421", "messageId": "422", "endLine": 56, "endColumn": 24, "suggestions": "446"}, {"ruleId": "419", "severity": 2, "message": "420", "line": 93, "column": 21, "nodeType": "421", "messageId": "422", "endLine": 93, "endColumn": 24, "suggestions": "447"}, {"ruleId": "419", "severity": 2, "message": "420", "line": 4, "column": 34, "nodeType": "421", "messageId": "422", "endLine": 4, "endColumn": 37, "suggestions": "448"}, {"ruleId": "419", "severity": 2, "message": "420", "line": 26, "column": 39, "nodeType": "421", "messageId": "422", "endLine": 26, "endColumn": 42, "suggestions": "449"}, {"ruleId": "419", "severity": 2, "message": "420", "line": 51, "column": 39, "nodeType": "421", "messageId": "422", "endLine": 51, "endColumn": 42, "suggestions": "450"}, {"ruleId": "414", "severity": 2, "message": "451", "line": 18, "column": 48, "nodeType": null, "messageId": "416", "endLine": 18, "endColumn": 55}, "@typescript-eslint/no-unused-vars", "'Appointment' is defined but never used.", "unusedVar", "'request' is defined but never used.", "'Campaign' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["452", "453"], "'Message' is defined but never used.", "'Patient' is defined but never used.", "'useState' is defined but never used.", "'SidebarTrigger' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["454", "455", "456", "457"], ["458", "459", "460", "461"], ["462", "463", "464", "465"], ["466", "467", "468", "469"], "'_props' is defined but never used.", "'_' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["470"], ["471"], "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", ["472", "473"], ["474", "475"], ["476", "477"], ["478", "479"], ["480", "481"], "'options' is defined but never used.", {"messageId": "482", "fix": "483", "desc": "484"}, {"messageId": "485", "fix": "486", "desc": "487"}, {"messageId": "488", "data": "489", "fix": "490", "desc": "491"}, {"messageId": "488", "data": "492", "fix": "493", "desc": "494"}, {"messageId": "488", "data": "495", "fix": "496", "desc": "497"}, {"messageId": "488", "data": "498", "fix": "499", "desc": "500"}, {"messageId": "488", "data": "501", "fix": "502", "desc": "491"}, {"messageId": "488", "data": "503", "fix": "504", "desc": "494"}, {"messageId": "488", "data": "505", "fix": "506", "desc": "497"}, {"messageId": "488", "data": "507", "fix": "508", "desc": "500"}, {"messageId": "488", "data": "509", "fix": "510", "desc": "491"}, {"messageId": "488", "data": "511", "fix": "512", "desc": "494"}, {"messageId": "488", "data": "513", "fix": "514", "desc": "497"}, {"messageId": "488", "data": "515", "fix": "516", "desc": "500"}, {"messageId": "488", "data": "517", "fix": "518", "desc": "491"}, {"messageId": "488", "data": "519", "fix": "520", "desc": "494"}, {"messageId": "488", "data": "521", "fix": "522", "desc": "497"}, {"messageId": "488", "data": "523", "fix": "524", "desc": "500"}, {"messageId": "525", "fix": "526", "desc": "527"}, {"messageId": "525", "fix": "528", "desc": "527"}, {"messageId": "482", "fix": "529", "desc": "484"}, {"messageId": "485", "fix": "530", "desc": "487"}, {"messageId": "482", "fix": "531", "desc": "484"}, {"messageId": "485", "fix": "532", "desc": "487"}, {"messageId": "482", "fix": "533", "desc": "484"}, {"messageId": "485", "fix": "534", "desc": "487"}, {"messageId": "482", "fix": "535", "desc": "484"}, {"messageId": "485", "fix": "536", "desc": "487"}, {"messageId": "482", "fix": "537", "desc": "484"}, {"messageId": "485", "fix": "538", "desc": "487"}, "suggestUnknown", {"range": "539", "text": "540"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "541", "text": "542"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceWithAlt", {"alt": "543"}, {"range": "544", "text": "545"}, "Replace with `&quot;`.", {"alt": "546"}, {"range": "547", "text": "548"}, "Replace with `&ldquo;`.", {"alt": "549"}, {"range": "550", "text": "551"}, "Replace with `&#34;`.", {"alt": "552"}, {"range": "553", "text": "554"}, "Replace with `&rdquo;`.", {"alt": "543"}, {"range": "555", "text": "556"}, {"alt": "546"}, {"range": "557", "text": "558"}, {"alt": "549"}, {"range": "559", "text": "560"}, {"alt": "552"}, {"range": "561", "text": "562"}, {"alt": "543"}, {"range": "563", "text": "564"}, {"alt": "546"}, {"range": "565", "text": "566"}, {"alt": "549"}, {"range": "567", "text": "568"}, {"alt": "552"}, {"range": "569", "text": "570"}, {"alt": "543"}, {"range": "571", "text": "572"}, {"alt": "546"}, {"range": "573", "text": "574"}, {"alt": "549"}, {"range": "575", "text": "576"}, {"alt": "552"}, {"range": "577", "text": "578"}, "replaceEmptyInterfaceWithSuper", {"range": "579", "text": "580"}, "Replace empty interface with a type alias.", {"range": "581", "text": "582"}, {"range": "583", "text": "540"}, {"range": "584", "text": "542"}, {"range": "585", "text": "540"}, {"range": "586", "text": "542"}, {"range": "587", "text": "540"}, {"range": "588", "text": "542"}, {"range": "589", "text": "540"}, {"range": "590", "text": "542"}, {"range": "591", "text": "540"}, {"range": "592", "text": "542"}, [1536, 1539], "unknown", [1536, 1539], "never", "&quot;", [8864, 8979], "\n                      &quot;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", "&ldquo;", [8864, 8979], "\n                      &ldquo;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", "&#34;", [8864, 8979], "\n                      &#34;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", "&rdquo;", [8864, 8979], "\n                      &rdquo;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&quot;\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&ldquo;\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&#34;\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&rdquo;\n                    ", [9629, 9746], "\n                      &quot;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      &ldquo;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      &#34;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      &rdquo;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&quot;\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&ldquo;\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&#34;\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&rdquo;\n                    ", [724, 775], "type CommandDialogProps = DialogProps", [77, 164], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [1556, 1559], [1556, 1559], [2494, 2497], [2494, 2497], [143, 146], [143, 146], [510, 513], [510, 513], [1134, 1137], [1134, 1137]]