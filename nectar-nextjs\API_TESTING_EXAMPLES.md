# API Testing Examples - Nectar Saúde

Este documento contém exemplos de curl para testar todas as APIs do sistema Nectar Saúde.

## Configuração Base

```bash
# URL base da API
BASE_URL="http://localhost:3000"

# IMPORTANTE: Você precisa fazer login primeiro para obter o JWT token
# A ANON KEY não funciona para operações protegidas!

# 1. Primeiro, faça login:
curl -X POST ${BASE_URL}/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "sua-senha-aqui"
  }'

# 2. Use o access_token retornado nos headers:
HEADERS='-H "Content-Type: application/json" -H "Authorization: Bearer SEU_ACCESS_TOKEN_AQUI"'
```

## ⚠️ PROBLEMA DE AUTENTICAÇÃO RESOLVIDO

**Erro comum:** Usar a ANON KEY diretamente não funciona para APIs protegidas.

**Solução:**
1. <PERSON><PERSON><PERSON> login via `/api/auth/login`
2. Use o `access_token` retornado
3. Inclua o token no header `Authorization: Bearer TOKEN`

## 1. APPOINTMENTS API (Agendamentos)

### 1.1 Criar um novo agendamento
```bash
curl -X POST ${BASE_URL}/api/appointments \
  ${HEADERS} \
  -d '{
    "title": "Consulta de Rotina",
    "description": "Consulta médica de rotina para check-up",
    "patient_id": "patient-uuid-here",
    "start_time": "2024-01-15T14:00:00.000Z",
    "end_time": "2024-01-15T15:00:00.000Z",
    "type": "consultation",
    "price": 150.00,
    "status": "scheduled"
  }'
```

### 1.2 Listar agendamentos
```bash
# Listar todos os agendamentos
curl -X GET ${BASE_URL}/api/appointments ${HEADERS}

# Listar agendamentos por data (há dados de teste para 2024-01-15)
curl -X GET "${BASE_URL}/api/appointments?date=2024-01-15" ${HEADERS}

# Listar agendamentos por status
curl -X GET "${BASE_URL}/api/appointments?status=scheduled" ${HEADERS}
```

### 🧪 TESTE RÁPIDO COM DADOS EXISTENTES
```bash
# Há um paciente de teste: João Silva (ID: 99cfb665-96cf-4ee3-95e1-9b90a7a0db8f)
# Há um agendamento de teste para 2024-01-15

# Teste 1: Listar pacientes (deve retornar João Silva)
curl -X GET ${BASE_URL}/api/patients ${HEADERS}

# Teste 2: Listar agendamentos de 15/01/2024 (deve retornar 1 consulta)
curl -X GET "${BASE_URL}/api/appointments?date=2024-01-15" ${HEADERS}
```

### 1.3 Buscar agendamento específico
```bash
curl -X GET ${BASE_URL}/api/appointments/APPOINTMENT_ID ${HEADERS}
```

### 1.4 Atualizar agendamento
```bash
curl -X PATCH ${BASE_URL}/api/appointments/APPOINTMENT_ID \
  ${HEADERS} \
  -d '{
    "status": "confirmed",
    "notes": "Paciente confirmou presença"
  }'
```

### 1.5 Excluir agendamento
```bash
curl -X DELETE ${BASE_URL}/api/appointments/APPOINTMENT_ID ${HEADERS}
```

## 2. PATIENTS API (Pacientes)

### 2.1 Criar um novo paciente
```bash
curl -X POST ${BASE_URL}/api/patients \
  ${HEADERS} \
  -d '{
    "name": "João Silva",
    "email": "<EMAIL>",
    "phone": "(11) 99999-9999",
    "birth_date": "1985-03-15",
    "cpf": "123.456.789-00",
    "address": "Rua das Flores, 123 - São Paulo, SP",
    "notes": "Paciente com histórico de hipertensão"
  }'
```

### 2.2 Listar pacientes
```bash
# Listar todos os pacientes
curl -X GET ${BASE_URL}/api/patients ${HEADERS}

# Buscar pacientes por nome
curl -X GET "${BASE_URL}/api/patients?search=João" ${HEADERS}
```

### 2.3 Buscar paciente específico
```bash
curl -X GET ${BASE_URL}/api/patients/PATIENT_ID ${HEADERS}
```

### 2.4 Atualizar paciente
```bash
curl -X PATCH ${BASE_URL}/api/patients/PATIENT_ID \
  ${HEADERS} \
  -d '{
    "phone": "(11) 88888-8888",
    "address": "Nova Rua, 456 - São Paulo, SP"
  }'
```

### 2.5 Excluir paciente
```bash
curl -X DELETE ${BASE_URL}/api/patients/PATIENT_ID ${HEADERS}
```

## 3. MESSAGES API (Mensagens)

### 3.1 Enviar mensagem
```bash
curl -X POST ${BASE_URL}/api/messages \
  ${HEADERS} \
  -d '{
    "patient_id": "patient-uuid-here",
    "content": "Olá! Sua consulta está agendada para amanhã às 14h.",
    "direction": "outbound",
    "channel": "whatsapp"
  }'
```

### 3.2 Listar mensagens
```bash
# Listar todas as mensagens
curl -X GET ${BASE_URL}/api/messages ${HEADERS}

# Listar mensagens de um paciente específico
curl -X GET "${BASE_URL}/api/messages?patient_id=PATIENT_ID" ${HEADERS}
```

### 3.3 Marcar mensagem como lida
```bash
curl -X PATCH ${BASE_URL}/api/messages/MESSAGE_ID \
  ${HEADERS} \
  -d '{
    "status": "read"
  }'
```

## 4. CAMPAIGNS API (Campanhas)

### 4.1 Criar campanha
```bash
curl -X POST ${BASE_URL}/api/campaigns \
  ${HEADERS} \
  -d '{
    "name": "Campanha de Prevenção",
    "message": "Não esqueça de agendar seu check-up anual!",
    "channel": "whatsapp",
    "target_audience": "all",
    "scheduled_date": "2024-01-20T10:00:00.000Z"
  }'
```

### 4.2 Listar campanhas
```bash
curl -X GET ${BASE_URL}/api/campaigns ${HEADERS}
```

### 4.3 Enviar campanha
```bash
curl -X POST ${BASE_URL}/api/campaigns/CAMPAIGN_ID/send ${HEADERS}
```

### 4.4 Atualizar campanha
```bash
curl -X PATCH ${BASE_URL}/api/campaigns/CAMPAIGN_ID \
  ${HEADERS} \
  -d '{
    "name": "Campanha Atualizada",
    "message": "Mensagem atualizada da campanha"
  }'
```

### 4.5 Excluir campanha
```bash
curl -X DELETE ${BASE_URL}/api/campaigns/CAMPAIGN_ID ${HEADERS}
```

## 5. REPORTS API (Relatórios)

### 5.1 Obter estatísticas do dashboard
```bash
curl -X GET ${BASE_URL}/api/dashboard/stats ${HEADERS}
```

### 5.2 Obter relatórios detalhados
```bash
# Relatório dos últimos 30 dias
curl -X GET "${BASE_URL}/api/reports?range=30days" ${HEADERS}

# Relatório de período personalizado
curl -X GET "${BASE_URL}/api/reports?start_date=2024-01-01&end_date=2024-01-31" ${HEADERS}
```

### 5.3 Exportar relatório
```bash
# Exportar em PDF
curl -X GET "${BASE_URL}/api/reports/export?format=pdf&range=30days" ${HEADERS} \
  --output relatorio.pdf

# Exportar em Excel
curl -X GET "${BASE_URL}/api/reports/export?format=excel&range=30days" ${HEADERS} \
  --output relatorio.xlsx
```

## 6. SETTINGS API (Configurações)

### 6.1 Obter configurações
```bash
curl -X GET ${BASE_URL}/api/settings ${HEADERS}
```

### 6.2 Atualizar perfil
```bash
curl -X PATCH ${BASE_URL}/api/settings/profile \
  ${HEADERS} \
  -d '{
    "name": "Dr. João Silva",
    "specialty": "Cardiologia",
    "crm": "12345-SP",
    "clinic_name": "Clínica Coração Saudável"
  }'
```

### 6.3 Atualizar notificações
```bash
curl -X PATCH ${BASE_URL}/api/settings/notifications \
  ${HEADERS} \
  -d '{
    "email_appointments": true,
    "whatsapp_notifications": true,
    "sms_appointments": false
  }'
```

### 6.4 Atualizar integrações
```bash
curl -X PATCH ${BASE_URL}/api/settings/integrations \
  ${HEADERS} \
  -d '{
    "whatsapp_token": "your-whatsapp-token",
    "whatsapp_phone": "+5511999999999"
  }'
```

## 7. AUTH API (Autenticação)

### 7.1 Login
```bash
curl -X POST ${BASE_URL}/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "sua-senha"
  }'
```

### 7.2 Registro
```bash
curl -X POST ${BASE_URL}/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "nova-senha",
    "name": "Novo Usuário"
  }'
```

### 7.3 Logout
```bash
curl -X POST ${BASE_URL}/api/auth/logout ${HEADERS}
```

### 7.4 Refresh Token
```bash
curl -X POST ${BASE_URL}/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "your-refresh-token"
  }'
```

## Notas Importantes

1. **Autenticação**: Substitua `YOUR_JWT_TOKEN_HERE` pelo token JWT real obtido após o login
2. **IDs**: Substitua `APPOINTMENT_ID`, `PATIENT_ID`, etc. pelos IDs reais dos recursos
3. **Datas**: Use formato ISO 8601 para datas (YYYY-MM-DDTHH:mm:ss.sssZ)
4. **Status de Agendamento**: `scheduled`, `confirmed`, `completed`, `cancelled`
5. **Tipos de Consulta**: `consultation`, `return`, `teleconsultation`
6. **Canais de Mensagem**: `whatsapp`, `email`, `sms`

## Testando no Postman

Para usar estes exemplos no Postman:

1. Importe como coleção ou crie requests individuais
2. Configure variáveis de ambiente para `BASE_URL` e `JWT_TOKEN`
3. Use `{{BASE_URL}}` e `{{JWT_TOKEN}}` nos requests
4. Configure pre-request scripts para renovar tokens automaticamente se necessário
