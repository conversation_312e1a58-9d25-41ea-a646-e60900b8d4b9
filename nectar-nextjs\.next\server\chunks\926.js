exports.id=926,exports.ids=[926],exports.modules={27170:(e,a,t)=>{"use strict";t.d(a,{DashboardLayout:()=>s});var r=t(12907);let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\DashboardLayout.tsx","DashboardLayout");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\DashboardLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\DashboardLayout.tsx","default")},31224:(e,a,t)=>{Promise.resolve().then(t.bind(t,27170))},40952:(e,a,t)=>{Promise.resolve().then(t.bind(t,88845))},63144:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});var r=t(37413),s=t(27170);function i({children:e}){return(0,r.jsx)(s.DashboardLayout,{children:e})}},88845:(e,a,t)=>{"use strict";t.d(a,{DashboardLayout:()=>W});var r=t(60687),s=t(43210),i=t(8730),d=t(24224),n=t(51214),o=t(4780),l=t(29523),c=t(89667),u=t(62369);let f=s.forwardRef(({className:e,orientation:a="horizontal",decorative:t=!0,...s},i)=>(0,r.jsx)(u.b,{ref:i,decorative:t,orientation:a,className:(0,o.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...s}));f.displayName=u.b.displayName;var b=t(26134),m=t(11860);let p=b.bL;b.l9,b.bm;let h=b.ZL,x=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(b.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:t}));x.displayName=b.hJ.displayName;let g=(0,d.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=s.forwardRef(({side:e="right",className:a,children:t,...s},i)=>(0,r.jsxs)(h,{children:[(0,r.jsx)(x,{}),(0,r.jsxs)(b.UC,{ref:i,className:(0,o.cn)(g({side:e}),a),...s,children:[t,(0,r.jsxs)(b.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));function w({className:e,...a}){return(0,r.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",e),...a})}v.displayName=b.UC.displayName,s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(b.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold text-foreground",e),...a})).displayName=b.hE.displayName,s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(b.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...a})).displayName=b.VY.displayName;var j=t(76242);let N=s.createContext(null);function y(){let e=s.useContext(N);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let S=s.forwardRef(({defaultOpen:e=!0,open:a,onOpenChange:t,className:i,style:d,children:n,...l},c)=>{let u=function(){let[e,a]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),t=()=>{a(window.innerWidth<768)};return e.addEventListener("change",t),a(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[f,b]=s.useState(!1),[m,p]=s.useState(e),h=a??m,x=s.useCallback(e=>{let a="function"==typeof e?e(h):e;t?t(a):p(a),document.cookie=`sidebar:state=${a}; path=/; max-age=604800`},[t,h]),g=s.useCallback(()=>u?b(e=>!e):x(e=>!e),[u,x,b]);s.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let v=h?"expanded":"collapsed",w=s.useMemo(()=>({state:v,open:h,setOpen:x,isMobile:u,openMobile:f,setOpenMobile:b,toggleSidebar:g}),[v,h,x,u,f,b,g]);return(0,r.jsx)(N.Provider,{value:w,children:(0,r.jsx)(j.Bc,{delayDuration:0,children:(0,r.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...d},className:(0,o.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",i),ref:c,...l,children:n})})})});S.displayName="SidebarProvider";let k=s.forwardRef(({side:e="left",variant:a="sidebar",collapsible:t="offcanvas",className:s,children:i,...d},n)=>{let{isMobile:l,state:c,openMobile:u,setOpenMobile:f}=y();return"none"===t?(0,r.jsx)("div",{className:(0,o.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",s),ref:n,...d,children:i}):l?(0,r.jsx)(p,{open:u,onOpenChange:f,...d,children:(0,r.jsx)(v,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:i})})}):(0,r.jsxs)("div",{ref:n,className:"group peer hidden md:block text-sidebar-foreground","data-state":c,"data-collapsible":"collapsed"===c?t:"","data-variant":a,"data-side":e,children:[(0,r.jsx)("div",{className:(0,o.cn)("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,r.jsx)("div",{className:(0,o.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",s),...d,children:(0,r.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:i})})]})});k.displayName="Sidebar";let R=s.forwardRef(({className:e,onClick:a,...t},s)=>{let{toggleSidebar:i}=y();return(0,r.jsxs)(l.$,{ref:s,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,o.cn)("h-7 w-7",e),onClick:e=>{a?.(e),i()},...t,children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});R.displayName="SidebarTrigger",s.forwardRef(({className:e,...a},t)=>{let{toggleSidebar:s}=y();return(0,r.jsx)("button",{ref:t,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:s,title:"Toggle Sidebar",className:(0,o.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...a})}).displayName="SidebarRail",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("main",{ref:t,className:(0,o.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...a})).displayName="SidebarInset",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(c.p,{ref:t,"data-sidebar":"input",className:(0,o.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...a})).displayName="SidebarInput",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"header",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...a})).displayName="SidebarHeader",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"footer",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...a})).displayName="SidebarFooter",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(f,{ref:t,"data-sidebar":"separator",className:(0,o.cn)("mx-2 w-auto bg-sidebar-border",e),...a})).displayName="SidebarSeparator";let z=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"content",className:(0,o.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...a}));z.displayName="SidebarContent";let C=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"group",className:(0,o.cn)("relative flex w-full min-w-0 flex-col p-2",e),...a}));C.displayName="SidebarGroup";let D=s.forwardRef(({className:e,asChild:a=!1,...t},s)=>{let d=a?i.DX:"div";return(0,r.jsx)(d,{ref:s,"data-sidebar":"group-label",className:(0,o.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...t})});D.displayName="SidebarGroupLabel",s.forwardRef(({className:e,asChild:a=!1,...t},s)=>{let d=a?i.DX:"button";return(0,r.jsx)(d,{ref:s,"data-sidebar":"group-action",className:(0,o.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...t})}).displayName="SidebarGroupAction";let _=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"group-content",className:(0,o.cn)("w-full text-sm",e),...a}));_.displayName="SidebarGroupContent";let A=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("ul",{ref:t,"data-sidebar":"menu",className:(0,o.cn)("flex w-full min-w-0 flex-col gap-1",e),...a}));A.displayName="SidebarMenu";let L=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("li",{ref:t,"data-sidebar":"menu-item",className:(0,o.cn)("group/menu-item relative",e),...a}));L.displayName="SidebarMenuItem";let M=(0,d.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),E=s.forwardRef(({asChild:e=!1,isActive:a=!1,variant:t="default",size:s="default",tooltip:d,className:n,...l},c)=>{let u=e?i.DX:"button",{isMobile:f,state:b}=y(),m=(0,r.jsx)(u,{ref:c,"data-sidebar":"menu-button","data-size":s,"data-active":a,className:(0,o.cn)(M({variant:t,size:s}),n),...l});return d?("string"==typeof d&&(d={children:d}),(0,r.jsxs)(j.m_,{children:[(0,r.jsx)(j.k$,{asChild:!0,children:m}),(0,r.jsx)(j.ZI,{side:"right",align:"center",hidden:"collapsed"!==b||f,...d})]})):m});E.displayName="SidebarMenuButton",s.forwardRef(({className:e,asChild:a=!1,showOnHover:t=!1,...s},d)=>{let n=a?i.DX:"button";return(0,r.jsx)(n,{ref:d,"data-sidebar":"menu-action",className:(0,o.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...s})}).displayName="SidebarMenuAction",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"menu-badge",className:(0,o.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...a})).displayName="SidebarMenuBadge",s.forwardRef(({className:e,showIcon:a=!1,...t},i)=>{let d=s.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,r.jsxs)("div",{ref:i,"data-sidebar":"menu-skeleton",className:(0,o.cn)("rounded-md h-8 flex gap-2 px-2 items-center",e),...t,children:[a&&(0,r.jsx)(w,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,r.jsx)(w,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":d}})]})}).displayName="SidebarMenuSkeleton",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("ul",{ref:t,"data-sidebar":"menu-sub",className:(0,o.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...a})).displayName="SidebarMenuSub",s.forwardRef(({...e},a)=>(0,r.jsx)("li",{ref:a,...e})).displayName="SidebarMenuSubItem",s.forwardRef(({asChild:e=!1,size:a="md",isActive:t,className:s,...d},n)=>{let l=e?i.DX:"a";return(0,r.jsx)(l,{ref:n,"data-sidebar":"menu-sub-button","data-size":a,"data-active":t,className:(0,o.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===a&&"text-xs","md"===a&&"text-sm","group-data-[collapsible=icon]:hidden",s),...d})}).displayName="SidebarMenuSubButton";var I=t(85814),P=t.n(I),U=t(16189),X=t(49625),$=t(40228),B=t(41312),G=t(58887),T=t(20798),V=t(53411),F=t(84027),O=t(67760);let q=[{title:"Dashboard",url:"/dashboard",icon:X.A},{title:"Agenda",url:"/dashboard/agenda",icon:$.A},{title:"Pacientes",url:"/dashboard/pacientes",icon:B.A},{title:"Mensagens",url:"/dashboard/mensagens",icon:G.A},{title:"Campanhas",url:"/dashboard/campanhas",icon:T.A},{title:"Relat\xf3rios",url:"/dashboard/relatorios",icon:V.A},{title:"Configura\xe7\xf5es",url:"/dashboard/configuracoes",icon:F.A}];function J(){let{state:e}=y(),a=(0,U.usePathname)(),t=e=>a===e;return(0,r.jsx)(k,{collapsible:"icon",children:(0,r.jsxs)(z,{children:[(0,r.jsx)("div",{className:"p-6 border-b",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(O.A,{className:"h-8 w-8 text-primary mr-2"}),"expanded"===e&&(0,r.jsx)("span",{className:"text-xl font-bold text-foreground",children:"Nectar Sa\xfade"})]})}),(0,r.jsxs)(C,{children:[(0,r.jsx)(D,{children:"Menu Principal"}),(0,r.jsx)(_,{children:(0,r.jsx)(A,{children:q.map(a=>(0,r.jsx)(L,{children:(0,r.jsx)(E,{asChild:!0,children:(0,r.jsxs)(P(),{href:a.url,className:t(a.url)?"bg-primary/10 text-primary font-medium border-r-2 border-primary":"hover:bg-muted/50",children:[(0,r.jsx)(a.icon,{className:"mr-3 h-5 w-5"}),"expanded"===e&&(0,r.jsx)("span",{children:a.title})]})})},a.title))})})]})]})})}var K=t(87979);let W=({children:e})=>{let{signOut:a}=(0,K.A)();return(0,r.jsx)(S,{children:(0,r.jsxs)("div",{className:"min-h-screen flex w-full bg-background",children:[(0,r.jsx)(J,{}),(0,r.jsxs)("main",{className:"flex-1",children:[(0,r.jsxs)("header",{className:"h-16 border-b bg-card/50 backdrop-blur-sm flex items-center justify-between px-6",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(R,{className:"mr-4"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-success rounded-full"}),(0,r.jsx)("span",{children:"Online"})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",onClick:a,children:"Sair"})]})]}),(0,r.jsx)("div",{className:"p-6",children:e})]})]})})}}};