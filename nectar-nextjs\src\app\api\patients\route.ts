import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Patient = Tables<'patients'>
type PatientInsert = TablesInsert<'patients'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('Fetching patients for user:', userId);

      const { data: patients, error } = await supabase
        .from('patients')
        .select('*')
        .eq('user_id', userId)
        .order('name')

      if (error) {
        console.error('Supabase error:', error);
        return handleApiError(error)
      }

      console.log('Found patients:', patients?.length || 0);
      return createApiResponse(patients || [])
    } catch (error) {
      console.error('API error:', error);
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const patientData: PatientInsert = {
        ...body,
        user_id: userId,
        birth_date: body.birth_date || null
      }

      const { data: patient, error } = await supabase
        .from('patients')
        .insert(patientData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(patient, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
