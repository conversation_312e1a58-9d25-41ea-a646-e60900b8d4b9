{"version": 3, "file": "base64url.test.js", "sourceRoot": "", "sources": ["../../../src/utils/base64url.test.ts"], "names": [], "mappings": ";;AAAA,mCAAgD;AAChD,2CAKqB;AAErB,MAAM,QAAQ,GAAG;IACf,GAAG;IACH,IAAI;IACJ,KAAK;IACL,MAAM;IACN,aAAa;IACb,mBAAmB;IACnB,6BAA6B;IAC7B,sFAAsF;CACvF,CAAC;AAEF,IAAA,iBAAQ,EAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAA,aAAI,EAAC,WAAW,OAAO,GAAG,EAAE,GAAG,EAAE;YAC/B,IAAA,eAAM,EAAC,IAAA,6BAAiB,EAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CACxC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAC3C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAA,aAAI,EAAC,WAAW,OAAO,GAAG,EAAE,GAAG,EAAE;YAC/B,IAAA,eAAM,EACJ,IAAA,+BAAmB,EACjB,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CACvD,CACF,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,aAAI,EAAC,0CAA0C,EAAE,GAAG,EAAE;QACpD,IAAA,eAAM,EAAC,GAAG,EAAE;YACV,IAAA,+BAAmB,EAAC,GAAG,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,QAAQ,GAAG;IACf,CAAC,IAAI,CAAC,EAAE,WAAW;IACnB,CAAC,IAAI,CAAC,EAAE,WAAW;IACnB,CAAC,IAAI,CAAC,EAAE,WAAW;IACnB,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,oBAAoB;IAC/B,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,oBAAoB;IAC/B,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,oBAAoB;CAChC,CAAC;AAEF,IAAA,iBAAQ,EAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAA,aAAI,EAAC,uCAAuC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE;YAC/F,IAAA,eAAM,EAAC,GAAG,EAAE;gBACV,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;gBAC3C,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACvB,IAAA,0BAAc,EAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAA,aAAI,EAAC,mCAAmC,EAAE,GAAG,EAAE;QAC7C,MAAM,gBAAgB,GAAG,QAAQ,GAAG,CAAC,CAAC;QACtC,IAAA,eAAM,EAAC,GAAG,EAAE;YACV,IAAA,2BAAe,EAAC,gBAAgB,EAAE,GAAG,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,OAAO,CACR,IAAI,KAAK,CACP,mCAAmC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CACnE,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}