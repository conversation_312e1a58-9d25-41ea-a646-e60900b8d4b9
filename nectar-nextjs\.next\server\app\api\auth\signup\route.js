(()=>{var e={};e.id=887,e.ids=[887],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>u});var s=t(9866),i=t(4999);async function u(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://zmwdnemlzndjavlriyrc.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set(r,t,s))}catch{}}}})}},2679:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>c});var i=t(6559),u=t(8088),a=t(7719),n=t(2507),o=t(3171);async function c(e){try{let{email:r,password:t,name:s}=await e.json();if(!r||!t||!s)return(0,o.Wg)(void 0,"Email, password, and name are required",400);let i=await (0,n.U)(),{data:u,error:a}=await i.auth.signUp({email:r,password:t,options:{data:{name:s}}});if(a)return(0,o.Wg)(void 0,a.message,400);return(0,o.Wg)({user:u.user,session:u.session,message:"Please check your email to confirm your account"})}catch(e){return(0,o.hS)(e)}}let p=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signup\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3171:(e,r,t)=>{"use strict";t.d(r,{Wg:()=>u,hS:()=>n,ru:()=>a});var s=t(2190),i=t(2507);function u(e,r,t=200){return s.NextResponse.json({data:e,error:r},{status:t})}async function a(e,r){try{let e=await (0,i.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return u(void 0,"Unauthorized",401);return await r(t.id,e)}catch(e){return console.error("API Error:",e),u(void 0,e instanceof Error?e.message:"Internal server error",500)}}function n(e){return(console.error("API Error:",e),e?.code==="PGRST116")?u(void 0,"Resource not found",404):e?.code==="23505"?u(void 0,"Resource already exists",409):u(void 0,e?.message||"Internal server error",500)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,866],()=>t(2679));module.exports=s})();