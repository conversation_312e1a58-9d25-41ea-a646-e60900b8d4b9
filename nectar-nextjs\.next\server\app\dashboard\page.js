(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80559:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Unexpected token `div`. Expected jsx identifier\n    ,-[\x1b[36;1;4mC:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx\x1b[0m:89:1]\n \x1b[2m86\x1b[0m |   const recentAppointments = dashboardData?.recentAppointments || [];\n \x1b[2m87\x1b[0m | \n \x1b[2m88\x1b[0m |   return (\n \x1b[2m89\x1b[0m |     <div className="space-y-6">\n    : \x1b[35;1m     ^^^\x1b[0m\n \x1b[2m90\x1b[0m |         <div>\n \x1b[2m91\x1b[0m |           <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>\n \x1b[2m92\x1b[0m |           <p className="text-muted-foreground">Vis\xe3o geral dos seus atendimentos e m\xe9tricas</p>\n    `----\n\n\nCaused by:\n    Syntax Error')},81630:e=>{"use strict";e.exports=require("http")},85764:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>x,routeModule:()=>c,tree:()=>u});var s=t(65239),n=t(48088),o=t(88170),a=t.n(o),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let u={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=["C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[524,822,17,318,704,542,926],()=>t(85764));module.exports=s})();