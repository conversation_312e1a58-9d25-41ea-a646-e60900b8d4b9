(()=>{var e={};e.id=105,e.ids=[105],e.modules={228:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1312:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3411:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3556:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(3210),n=(r(9522),r(9867));function s(){let[e,t]=(0,a.useState)(null),[r,s]=(0,a.useState)(null),[i,o]=(0,a.useState)(!0),{toast:l}=(0,n.dj)();return{user:e,session:r,loading:i,signIn:async(e,t)=>{try{let r=await fetch("/api/auth/signin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(!r.ok)return l({title:"Erro no login",description:a.error,variant:"destructive"}),{error:a.error};return{error:null}}catch(e){return l({title:"Erro no login",description:"Erro inesperado ao fazer login",variant:"destructive"}),{error:e}}},signUp:async(e,t,r)=>{try{let a=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t,name:r})}),n=await a.json();if(!a.ok)return l({title:"Erro no cadastro",description:n.error,variant:"destructive"}),{error:n.error};return l({title:"Cadastro realizado",description:n.message||"Verifique seu email para confirmar a conta"}),{error:null}}catch(e){return l({title:"Erro no cadastro",description:"Erro inesperado ao fazer cadastro",variant:"destructive"}),{error:e}}},signOut:async()=>{try{await fetch("/api/auth/signout",{method:"POST"}),l({title:"Logout realizado",description:"Voc\xea foi desconectado com sucesso"})}catch(e){console.error("Error signing out:",e)}}}}},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4672:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tW});var a,n,s=r(687),i=r(3210),o=r(4493),l=r(9523),d=r(228),c=r(2688);let u=(0,c.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);var f=r(1312),p=r(8887);let m=(0,c.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var h=r(3411);let x=(0,c.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var g=r(6349),v=r(5336),b=r(3556),y=r(8730),w=r(4224);let j=(0,c.A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);var N=r(4780),k=r(9667),E=r(4163),R="horizontal",A=["horizontal","vertical"],S=i.forwardRef((e,t)=>{var r;let{decorative:a,orientation:n=R,...i}=e,o=(r=n,A.includes(r))?n:R;return(0,s.jsx)(E.sG.div,{"data-orientation":o,...a?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...i,ref:t})});S.displayName="Separator";let C=i.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...a},n)=>(0,s.jsx)(S,{ref:n,decorative:r,orientation:t,className:(0,N.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));C.displayName=S.displayName;var M=r(569),P=r(8599),D=r(1273),_=r(6963),T=r(5551),z=r(1355),O=r(3495),I="focusScope.autoFocusOnMount",L="focusScope.autoFocusOnUnmount",q={bubbles:!1,cancelable:!0},F=i.forwardRef((e,t)=>{let{loop:r=!1,trapped:a=!1,onMountAutoFocus:n,onUnmountAutoFocus:o,...l}=e,[d,c]=i.useState(null),u=(0,O.c)(n),f=(0,O.c)(o),p=i.useRef(null),m=(0,P.s)(t,e=>c(e)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(a){let e=function(e){if(h.paused||!d)return;let t=e.target;d.contains(t)?p.current=t:Z(p.current,{select:!0})},t=function(e){if(h.paused||!d)return;let t=e.relatedTarget;null!==t&&(d.contains(t)||Z(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&Z(d)});return d&&r.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[a,d,h.paused]),i.useEffect(()=>{if(d){G.add(h);let e=document.activeElement;if(!d.contains(e)){let t=new CustomEvent(I,q);d.addEventListener(I,u),d.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let a of e)if(Z(a,{select:t}),document.activeElement!==r)return}(W(d).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&Z(d))}return()=>{d.removeEventListener(I,u),setTimeout(()=>{let t=new CustomEvent(L,q);d.addEventListener(L,f),d.dispatchEvent(t),t.defaultPrevented||Z(e??document.body,{select:!0}),d.removeEventListener(L,f),G.remove(h)},0)}}},[d,u,f,h]);let x=i.useCallback(e=>{if(!r&&!a||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[a,s]=function(e){let t=W(e);return[B(t,e),B(t.reverse(),e)]}(t);a&&s?e.shiftKey||n!==s?e.shiftKey&&n===a&&(e.preventDefault(),r&&Z(s,{select:!0})):(e.preventDefault(),r&&Z(a,{select:!0})):n===t&&e.preventDefault()}},[r,a,h.paused]);return(0,s.jsx)(E.sG.div,{tabIndex:-1,...l,ref:m,onKeyDown:x})});function W(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function B(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function Z(e,{select:t=!1}={}){if(e&&e.focus){var r;let a=document.activeElement;e.focus({preventScroll:!0}),e!==a&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}F.displayName="FocusScope";var G=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=$(e,t)).unshift(t)},remove(t){e=$(e,t),e[0]?.resume()}}}();function $(e,t){let r=[...e],a=r.indexOf(t);return -1!==a&&r.splice(a,1),r}var V=r(5028),K=r(6059),X=0;function U(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var H=function(){return(H=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)};function Y(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r}Object.create;Object.create;var J=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),Q="width-before-scroll-bar";function ee(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var et="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,er=new WeakMap;function ea(e){return e}var en=function(e){void 0===e&&(e={});var t,r,a,n,s=(t=null,void 0===r&&(r=ea),a=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=r(e,n);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(n=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){n=!0;var t=[];if(a.length){var r=a;a=[],r.forEach(e),t=a}var s=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(s)};i(),a={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),a}}}});return s.options=H({async:!0,ssr:!1},e),s}(),es=function(){},ei=i.forwardRef(function(e,t){var r,a,n,s,o=i.useRef(null),l=i.useState({onScrollCapture:es,onWheelCapture:es,onTouchMoveCapture:es}),d=l[0],c=l[1],u=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,x=e.shards,g=e.sideCar,v=e.noRelative,b=e.noIsolation,y=e.inert,w=e.allowPinchZoom,j=e.as,N=e.gapMode,k=Y(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=(r=[o,t],a=function(e){return r.forEach(function(t){return ee(t,e)})},(n=(0,i.useState)(function(){return{value:null,callback:a,facade:{get current(){return n.value},set current(value){var e=n.value;e!==value&&(n.value=value,n.callback(value,e))}}}})[0]).callback=a,s=n.facade,et(function(){var e=er.get(s);if(e){var t=new Set(e),a=new Set(r),n=s.current;t.forEach(function(e){a.has(e)||ee(e,null)}),a.forEach(function(e){t.has(e)||ee(e,n)})}er.set(s,r)},[r]),s),R=H(H({},k),d);return i.createElement(i.Fragment,null,h&&i.createElement(g,{sideCar:en,removeScrollBar:m,shards:x,noRelative:v,noIsolation:b,inert:y,setCallbacks:c,allowPinchZoom:!!w,lockRef:o,gapMode:N}),u?i.cloneElement(i.Children.only(f),H(H({},R),{ref:E})):i.createElement(void 0===j?"div":j,H({},R,{className:p,ref:E}),f))});ei.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ei.classNames={fullWidth:Q,zeroRight:J};var eo=function(e){var t=e.sideCar,r=Y(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return i.createElement(a,H({},r))};eo.isSideCarExport=!0;var el=function(){var e=0,t=null;return{add:function(a){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var s,i;(s=t).styleSheet?s.styleSheet.cssText=a:s.appendChild(document.createTextNode(a)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ed=function(){var e=el();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},ec=function(){var e=ed();return function(t){return e(t.styles,t.dynamic),null}},eu={left:0,top:0,right:0,gap:0},ef=function(e){return parseInt(e||"",10)||0},ep=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],a=t["padding"===e?"paddingTop":"marginTop"],n=t["padding"===e?"paddingRight":"marginRight"];return[ef(r),ef(a),ef(n)]},em=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return eu;var t=ep(e),r=document.documentElement.clientWidth,a=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,a-r+t[2]-t[0])}},eh=ec(),ex="data-scroll-locked",eg=function(e,t,r,a){var n=e.left,s=e.top,i=e.right,o=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(a,";\n   padding-right: ").concat(o,"px ").concat(a,";\n  }\n  body[").concat(ex,"] {\n    overflow: hidden ").concat(a,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(a,";"),"margin"===r&&"\n    padding-left: ".concat(n,"px;\n    padding-top: ").concat(s,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(o,"px ").concat(a,";\n    "),"padding"===r&&"padding-right: ".concat(o,"px ").concat(a,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(J," {\n    right: ").concat(o,"px ").concat(a,";\n  }\n  \n  .").concat(Q," {\n    margin-right: ").concat(o,"px ").concat(a,";\n  }\n  \n  .").concat(J," .").concat(J," {\n    right: 0 ").concat(a,";\n  }\n  \n  .").concat(Q," .").concat(Q," {\n    margin-right: 0 ").concat(a,";\n  }\n  \n  body[").concat(ex,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(o,"px;\n  }\n")},ev=function(){var e=parseInt(document.body.getAttribute(ex)||"0",10);return isFinite(e)?e:0},eb=function(){i.useEffect(function(){return document.body.setAttribute(ex,(ev()+1).toString()),function(){var e=ev()-1;e<=0?document.body.removeAttribute(ex):document.body.setAttribute(ex,e.toString())}},[])},ey=function(e){var t=e.noRelative,r=e.noImportant,a=e.gapMode,n=void 0===a?"margin":a;eb();var s=i.useMemo(function(){return em(n)},[n]);return i.createElement(eh,{styles:eg(s,!t,n,r?"":"!important")})},ew=!1;if("undefined"!=typeof window)try{var ej=Object.defineProperty({},"passive",{get:function(){return ew=!0,!0}});window.addEventListener("test",ej,ej),window.removeEventListener("test",ej,ej)}catch(e){ew=!1}var eN=!!ew&&{passive:!1},ek=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},eE=function(e,t){var r=t.ownerDocument,a=t;do{if("undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&(a=a.host),eR(e,a)){var n=eA(e,a);if(n[1]>n[2])return!0}a=a.parentNode}while(a&&a!==r.body);return!1},eR=function(e,t){return"v"===e?ek(t,"overflowY"):ek(t,"overflowX")},eA=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eS=function(e,t,r,a,n){var s,i=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),o=i*a,l=r.target,d=t.contains(l),c=!1,u=o>0,f=0,p=0;do{if(!l)break;var m=eA(e,l),h=m[0],x=m[1]-m[2]-i*h;(h||x)&&eR(e,l)&&(f+=x,p+=h);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!d&&l!==document.body||d&&(t.contains(l)||t===l));return u&&(n&&1>Math.abs(f)||!n&&o>f)?c=!0:!u&&(n&&1>Math.abs(p)||!n&&-o>p)&&(c=!0),c},eC=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eM=function(e){return[e.deltaX,e.deltaY]},eP=function(e){return e&&"current"in e?e.current:e},eD=0,e_=[];let eT=(a=function(e){var t=i.useRef([]),r=i.useRef([0,0]),a=i.useRef(),n=i.useState(eD++)[0],s=i.useState(ec)[0],o=i.useRef(e);i.useEffect(function(){o.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(n));var t=(function(e,t,r){if(r||2==arguments.length)for(var a,n=0,s=t.length;n<s;n++)!a&&n in t||(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eP),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(n))}),function(){document.body.classList.remove("block-interactivity-".concat(n)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(n))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!o.current.allowPinchZoom;var n,s=eC(e),i=r.current,l="deltaX"in e?e.deltaX:i[0]-s[0],d="deltaY"in e?e.deltaY:i[1]-s[1],c=e.target,u=Math.abs(l)>Math.abs(d)?"h":"v";if("touches"in e&&"h"===u&&"range"===c.type)return!1;var f=eE(u,c);if(!f)return!0;if(f?n=u:(n="v"===u?"h":"v",f=eE(u,c)),!f)return!1;if(!a.current&&"changedTouches"in e&&(l||d)&&(a.current=n),!n)return!0;var p=a.current||n;return eS(p,t,e,"h"===p?l:d,!0)},[]),d=i.useCallback(function(e){if(e_.length&&e_[e_.length-1]===s){var r="deltaY"in e?eM(e):eC(e),a=t.current.filter(function(t){var a;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(a=t.delta,a[0]===r[0]&&a[1]===r[1])})[0];if(a&&a.should){e.cancelable&&e.preventDefault();return}if(!a){var n=(o.current.shards||[]).map(eP).filter(Boolean).filter(function(t){return t.contains(e.target)});(n.length>0?l(e,n[0]):!o.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,r,a,n){var s={name:e,delta:r,target:a,should:n,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(a)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),u=i.useCallback(function(e){r.current=eC(e),a.current=void 0},[]),f=i.useCallback(function(t){c(t.type,eM(t),t.target,l(t,e.lockRef.current))},[]),p=i.useCallback(function(t){c(t.type,eC(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return e_.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",d,eN),document.addEventListener("touchmove",d,eN),document.addEventListener("touchstart",u,eN),function(){e_=e_.filter(function(e){return e!==s}),document.removeEventListener("wheel",d,eN),document.removeEventListener("touchmove",d,eN),document.removeEventListener("touchstart",u,eN)}},[]);var m=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(s,{styles:"\n  .block-interactivity-".concat(n," {pointer-events: none;}\n  .allow-interactivity-").concat(n," {pointer-events: all;}\n")}):null,m?i.createElement(ey,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},en.useMedium(a),eo);var ez=i.forwardRef(function(e,t){return i.createElement(ei,H({},e,{ref:t,sideCar:eT}))});ez.classNames=ei.classNames;var eO=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eI=new WeakMap,eL=new WeakMap,eq={},eF=0,eW=function(e){return e&&(e.host||eW(e.parentNode))},eB=function(e,t,r,a){var n=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=eW(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eq[r]||(eq[r]=new WeakMap);var s=eq[r],i=[],o=new Set,l=new Set(n),d=function(e){!e||o.has(e)||(o.add(e),d(e.parentNode))};n.forEach(d);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(o.has(e))c(e);else try{var t=e.getAttribute(a),n=null!==t&&"false"!==t,l=(eI.get(e)||0)+1,d=(s.get(e)||0)+1;eI.set(e,l),s.set(e,d),i.push(e),1===l&&n&&eL.set(e,!0),1===d&&e.setAttribute(r,"true"),n||e.setAttribute(a,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),o.clear(),eF++,function(){i.forEach(function(e){var t=eI.get(e)-1,n=s.get(e)-1;eI.set(e,t),s.set(e,n),t||(eL.has(e)||e.removeAttribute(a),eL.delete(e)),n||e.removeAttribute(r)}),--eF||(eI=new WeakMap,eI=new WeakMap,eL=new WeakMap,eq={})}},eZ=function(e,t,r){void 0===r&&(r="data-aria-hidden");var a=Array.from(Array.isArray(e)?e:[e]),n=t||eO(e);return n?(a.push.apply(a,Array.from(n.querySelectorAll("[aria-live], script"))),eB(a,n,r,"aria-hidden")):function(){return null}},eG="Dialog",[e$,eV]=(0,D.A)(eG),[eK,eX]=e$(eG),eU=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:o,modal:l=!0}=e,d=i.useRef(null),c=i.useRef(null),[u,f]=(0,T.i)({prop:a,defaultProp:n??!1,onChange:o,caller:eG});return(0,s.jsx)(eK,{scope:t,triggerRef:d,contentRef:c,contentId:(0,_.B)(),titleId:(0,_.B)(),descriptionId:(0,_.B)(),open:u,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),modal:l,children:r})};eU.displayName=eG;var eH="DialogTrigger";i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=eX(eH,r),i=(0,P.s)(t,n.triggerRef);return(0,s.jsx)(E.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":ti(n.open),...a,ref:i,onClick:(0,M.m)(e.onClick,n.onOpenToggle)})}).displayName=eH;var eY="DialogPortal",[eJ,eQ]=e$(eY,{forceMount:void 0}),e0=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,o=eX(eY,t);return(0,s.jsx)(eJ,{scope:t,forceMount:r,children:i.Children.map(a,e=>(0,s.jsx)(K.C,{present:r||o.open,children:(0,s.jsx)(V.Z,{asChild:!0,container:n,children:e})}))})};e0.displayName=eY;var e1="DialogOverlay",e2=i.forwardRef((e,t)=>{let r=eQ(e1,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,i=eX(e1,e.__scopeDialog);return i.modal?(0,s.jsx)(K.C,{present:a||i.open,children:(0,s.jsx)(e4,{...n,ref:t})}):null});e2.displayName=e1;var e3=(0,y.TL)("DialogOverlay.RemoveScroll"),e4=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=eX(e1,r);return(0,s.jsx)(ez,{as:e3,allowPinchZoom:!0,shards:[n.contentRef],children:(0,s.jsx)(E.sG.div,{"data-state":ti(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),e5="DialogContent",e8=i.forwardRef((e,t)=>{let r=eQ(e5,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,i=eX(e5,e.__scopeDialog);return(0,s.jsx)(K.C,{present:a||i.open,children:i.modal?(0,s.jsx)(e7,{...n,ref:t}):(0,s.jsx)(e6,{...n,ref:t})})});e8.displayName=e5;var e7=i.forwardRef((e,t)=>{let r=eX(e5,e.__scopeDialog),a=i.useRef(null),n=(0,P.s)(t,r.contentRef,a);return i.useEffect(()=>{let e=a.current;if(e)return eZ(e)},[]),(0,s.jsx)(e9,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,M.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,M.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,M.m)(e.onFocusOutside,e=>e.preventDefault())})}),e6=i.forwardRef((e,t)=>{let r=eX(e5,e.__scopeDialog),a=i.useRef(!1),n=i.useRef(!1);return(0,s.jsx)(e9,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),e9=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:n,onCloseAutoFocus:o,...l}=e,d=eX(e5,r),c=i.useRef(null),u=(0,P.s)(t,c);return i.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??U()),document.body.insertAdjacentElement("beforeend",e[1]??U()),X++,()=>{1===X&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),X--}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(F,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:n,onUnmountAutoFocus:o,children:(0,s.jsx)(z.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":ti(d.open),...l,ref:u,onDismiss:()=>d.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(tc,{titleId:d.titleId}),(0,s.jsx)(tu,{contentRef:c,descriptionId:d.descriptionId})]})]})}),te="DialogTitle",tt=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=eX(te,r);return(0,s.jsx)(E.sG.h2,{id:n.titleId,...a,ref:t})});tt.displayName=te;var tr="DialogDescription",ta=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=eX(tr,r);return(0,s.jsx)(E.sG.p,{id:n.descriptionId,...a,ref:t})});ta.displayName=tr;var tn="DialogClose",ts=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=eX(tn,r);return(0,s.jsx)(E.sG.button,{type:"button",...a,ref:t,onClick:(0,M.m)(e.onClick,()=>n.onOpenChange(!1))})});function ti(e){return e?"open":"closed"}ts.displayName=tn;var to="DialogTitleWarning",[tl,td]=(0,D.q)(to,{contentName:e5,titleName:te,docsSlug:"dialog"}),tc=({titleId:e})=>{let t=td(to),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},tu=({contentRef:e,descriptionId:t})=>{let r=td("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return i.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(a))},[a,e,t]),null},tf=r(1860);let tp=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(e2,{className:(0,N.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));tp.displayName=e2.displayName;let tm=(0,w.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),th=i.forwardRef(({side:e="right",className:t,children:r,...a},n)=>(0,s.jsxs)(e0,{children:[(0,s.jsx)(tp,{}),(0,s.jsxs)(e8,{ref:n,className:(0,N.cn)(tm({side:e}),t),...a,children:[r,(0,s.jsxs)(ts,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,s.jsx)(tf.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));function tx({className:e,...t}){return(0,s.jsx)("div",{className:(0,N.cn)("animate-pulse rounded-md bg-muted",e),...t})}th.displayName=e8.displayName,i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(tt,{ref:r,className:(0,N.cn)("text-lg font-semibold text-foreground",e),...t})).displayName=tt.displayName,i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(ta,{ref:r,className:(0,N.cn)("text-sm text-muted-foreground",e),...t})).displayName=ta.displayName;var tg=r(6242);let tv=i.createContext(null);function tb(){let e=i.useContext(tv);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let ty=i.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:r,className:a,style:n,children:o,...l},d)=>{let c=function(){let[e,t]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}(),[u,f]=i.useState(!1),[p,m]=i.useState(e),h=t??p,x=i.useCallback(e=>{let t="function"==typeof e?e(h):e;r?r(t):m(t),document.cookie=`sidebar:state=${t}; path=/; max-age=604800`},[r,h]),g=i.useCallback(()=>c?f(e=>!e):x(e=>!e),[c,x,f]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let v=h?"expanded":"collapsed",b=i.useMemo(()=>({state:v,open:h,setOpen:x,isMobile:c,openMobile:u,setOpenMobile:f,toggleSidebar:g}),[v,h,x,c,u,f,g]);return(0,s.jsx)(tv.Provider,{value:b,children:(0,s.jsx)(tg.Bc,{delayDuration:0,children:(0,s.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...n},className:(0,N.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",a),ref:d,...l,children:o})})})});ty.displayName="SidebarProvider";let tw=i.forwardRef(({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:a,children:n,...i},o)=>{let{isMobile:l,state:d,openMobile:c,setOpenMobile:u}=tb();return"none"===r?(0,s.jsx)("div",{className:(0,N.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",a),ref:o,...i,children:n}):l?(0,s.jsx)(eU,{open:c,onOpenChange:u,...i,children:(0,s.jsx)(th,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:n})})}):(0,s.jsxs)("div",{ref:o,className:"group peer hidden md:block text-sidebar-foreground","data-state":d,"data-collapsible":"collapsed"===d?r:"","data-variant":t,"data-side":e,children:[(0,s.jsx)("div",{className:(0,N.cn)("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,s.jsx)("div",{className:(0,N.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",a),...i,children:(0,s.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:n})})]})});tw.displayName="Sidebar";let tj=i.forwardRef(({className:e,onClick:t,...r},a)=>{let{toggleSidebar:n}=tb();return(0,s.jsxs)(l.$,{ref:a,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,N.cn)("h-7 w-7",e),onClick:e=>{t?.(e),n()},...r,children:[(0,s.jsx)(j,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});tj.displayName="SidebarTrigger",i.forwardRef(({className:e,...t},r)=>{let{toggleSidebar:a}=tb();return(0,s.jsx)("button",{ref:r,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:a,title:"Toggle Sidebar",className:(0,N.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}).displayName="SidebarRail",i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("main",{ref:r,className:(0,N.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t})).displayName="SidebarInset",i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(k.p,{ref:r,"data-sidebar":"input",className:(0,N.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t})).displayName="SidebarInput",i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,"data-sidebar":"header",className:(0,N.cn)("flex flex-col gap-2 p-2",e),...t})).displayName="SidebarHeader",i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,"data-sidebar":"footer",className:(0,N.cn)("flex flex-col gap-2 p-2",e),...t})).displayName="SidebarFooter",i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(C,{ref:r,"data-sidebar":"separator",className:(0,N.cn)("mx-2 w-auto bg-sidebar-border",e),...t})).displayName="SidebarSeparator";let tN=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,"data-sidebar":"content",className:(0,N.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));tN.displayName="SidebarContent";let tk=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,"data-sidebar":"group",className:(0,N.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t}));tk.displayName="SidebarGroup";let tE=i.forwardRef(({className:e,asChild:t=!1,...r},a)=>{let n=t?y.DX:"div";return(0,s.jsx)(n,{ref:a,"data-sidebar":"group-label",className:(0,N.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})});tE.displayName="SidebarGroupLabel",i.forwardRef(({className:e,asChild:t=!1,...r},a)=>{let n=t?y.DX:"button";return(0,s.jsx)(n,{ref:a,"data-sidebar":"group-action",className:(0,N.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...r})}).displayName="SidebarGroupAction";let tR=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,"data-sidebar":"group-content",className:(0,N.cn)("w-full text-sm",e),...t}));tR.displayName="SidebarGroupContent";let tA=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("ul",{ref:r,"data-sidebar":"menu",className:(0,N.cn)("flex w-full min-w-0 flex-col gap-1",e),...t}));tA.displayName="SidebarMenu";let tS=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("li",{ref:r,"data-sidebar":"menu-item",className:(0,N.cn)("group/menu-item relative",e),...t}));tS.displayName="SidebarMenuItem";let tC=(0,w.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),tM=i.forwardRef(({asChild:e=!1,isActive:t=!1,variant:r="default",size:a="default",tooltip:n,className:i,...o},l)=>{let d=e?y.DX:"button",{isMobile:c,state:u}=tb(),f=(0,s.jsx)(d,{ref:l,"data-sidebar":"menu-button","data-size":a,"data-active":t,className:(0,N.cn)(tC({variant:r,size:a}),i),...o});return n?("string"==typeof n&&(n={children:n}),(0,s.jsxs)(tg.m_,{children:[(0,s.jsx)(tg.k$,{asChild:!0,children:f}),(0,s.jsx)(tg.ZI,{side:"right",align:"center",hidden:"collapsed"!==u||c,...n})]})):f});tM.displayName="SidebarMenuButton",i.forwardRef(({className:e,asChild:t=!1,showOnHover:r=!1,...a},n)=>{let i=t?y.DX:"button";return(0,s.jsx)(i,{ref:n,"data-sidebar":"menu-action",className:(0,N.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",r&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...a})}).displayName="SidebarMenuAction",i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,"data-sidebar":"menu-badge",className:(0,N.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t})).displayName="SidebarMenuBadge",i.forwardRef(({className:e,showIcon:t=!1,...r},a)=>{let n=i.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,s.jsxs)("div",{ref:a,"data-sidebar":"menu-skeleton",className:(0,N.cn)("rounded-md h-8 flex gap-2 px-2 items-center",e),...r,children:[t&&(0,s.jsx)(tx,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,s.jsx)(tx,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":n}})]})}).displayName="SidebarMenuSkeleton",i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("ul",{ref:r,"data-sidebar":"menu-sub",className:(0,N.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})).displayName="SidebarMenuSub",i.forwardRef(({...e},t)=>(0,s.jsx)("li",{ref:t,...e})).displayName="SidebarMenuSubItem",i.forwardRef(({asChild:e=!1,size:t="md",isActive:r,className:a,...n},i)=>{let o=e?y.DX:"a";return(0,s.jsx)(o,{ref:i,"data-sidebar":"menu-sub-button","data-size":t,"data-active":r,className:(0,N.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===t&&"text-xs","md"===t&&"text-sm","group-data-[collapsible=icon]:hidden",a),...n})}).displayName="SidebarMenuSubButton";var tP=r(5814),tD=r.n(tP),t_=r(6189);let tT=(0,c.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]),tz=(0,c.A)("megaphone",[["path",{d:"M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z",key:"q8bfy3"}],["path",{d:"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14",key:"1853fq"}],["path",{d:"M8 6v8",key:"15ugcq"}]]),tO=(0,c.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var tI=r(7760);let tL=[{title:"Dashboard",url:"/dashboard",icon:tT},{title:"Agenda",url:"/agenda",icon:d.A},{title:"Pacientes",url:"/pacientes",icon:f.A},{title:"Mensagens",url:"/mensagens",icon:p.A},{title:"Campanhas",url:"/campanhas",icon:tz},{title:"Relat\xf3rios",url:"/relatorios",icon:h.A},{title:"Configura\xe7\xf5es",url:"/configuracoes",icon:tO}];function tq(){let{state:e}=tb(),t=(0,t_.usePathname)(),r=e=>t===e;return(0,s.jsx)(tw,{collapsible:"icon",children:(0,s.jsxs)(tN,{children:[(0,s.jsx)("div",{className:"p-6 border-b",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(tI.A,{className:"h-8 w-8 text-primary mr-2"}),"expanded"===e&&(0,s.jsx)("span",{className:"text-xl font-bold text-foreground",children:"Nectar Sa\xfade"})]})}),(0,s.jsxs)(tk,{children:[(0,s.jsx)(tE,{children:"Menu Principal"}),(0,s.jsx)(tR,{children:(0,s.jsx)(tA,{children:tL.map(t=>(0,s.jsx)(tS,{children:(0,s.jsx)(tM,{asChild:!0,children:(0,s.jsxs)(tD(),{href:t.url,className:r(t.url)?"bg-primary/10 text-primary font-medium border-r-2 border-primary":"hover:bg-muted/50",children:[(0,s.jsx)(t.icon,{className:"mr-3 h-5 w-5"}),"expanded"===e&&(0,s.jsx)("span",{children:t.title})]})})},t.title))})})]})]})})}let tF=({children:e})=>{let{signOut:t}=(0,b.A)();return(0,s.jsx)(ty,{children:(0,s.jsxs)("div",{className:"min-h-screen flex w-full bg-background",children:[(0,s.jsx)(tq,{}),(0,s.jsxs)("main",{className:"flex-1",children:[(0,s.jsxs)("header",{className:"h-16 border-b bg-card/50 backdrop-blur-sm flex items-center justify-between px-6",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(tj,{className:"mr-4"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-success rounded-full"}),(0,s.jsx)("span",{children:"Online"})]}),(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:t,children:"Sair"})]})]}),(0,s.jsx)("div",{className:"p-6",children:e})]})]})})};function tW(){let[e,t]=(0,i.useState)(null),[r,a]=(0,i.useState)(!0),{user:n}=(0,b.A)();if(r)return(0,s.jsx)(tF,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})});let c=e?.stats||{todayAppointments:0,totalPatients:0,unreadMessages:0,monthlyRevenue:0},y=e?.recentAppointments||[];return(0,s.jsx)(tF,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Vis\xe3o geral dos seus atendimentos e m\xe9tricas"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)(o.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Consultas Hoje"}),(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:c.todayAppointments}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:[(0,s.jsx)(u,{className:"h-3 w-3 mr-1"}),"Consultas do dia"]})]})]}),(0,s.jsxs)(o.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Pacientes Ativos"}),(0,s.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:c.totalPatients}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:[(0,s.jsx)(u,{className:"h-3 w-3 mr-1"}),"Total cadastrados"]})]})]}),(0,s.jsxs)(o.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Mensagens"}),(0,s.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:"23"}),(0,s.jsxs)("p",{className:"text-xs text-yellow-600 flex items-center mt-1",children:[(0,s.jsx)(m,{className:"h-3 w-3 mr-1"}),c.unreadMessages," n\xe3o lidas"]})]})]}),(0,s.jsxs)(o.Zp,{className:"hover:shadow-lg transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Receita Mensal"}),(0,s.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-foreground",children:["R$ ",c.monthlyRevenue.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:[(0,s.jsx)(x,{className:"h-3 w-3 mr-1"}),"+15% vs m\xeas anterior"]})]})]})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"mr-2 h-5 w-5 text-primary"}),"Pr\xf3ximas Consultas"]}),(0,s.jsx)(o.BT,{children:"Agendamentos para hoje e amanh\xe3"})]}),(0,s.jsx)(o.Wu,{className:"space-y-4",children:0===y.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(d.A,{className:"mx-auto h-12 w-12 mb-4 opacity-50"}),(0,s.jsx)("p",{children:"Nenhuma consulta agendada para hoje"})]}):y.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border bg-card/50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-12 h-12 rounded-full bg-primary/10",children:(0,s.jsx)(g.A,{className:"h-5 w-5 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:e.patient}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.type})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-foreground",children:e.time}),"confirmed"===e.status?(0,s.jsx)(v.A,{className:"h-4 w-4 text-green-500"}):(0,s.jsx)(m,{className:"h-4 w-4 text-yellow-500"})]})]},t))})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"mr-2 h-5 w-5 text-primary"}),"Atividades Recentes"]}),(0,s.jsx)(o.BT,{children:"\xdaltimas intera\xe7\xf5es"})]}),(0,s.jsx)(o.Wu,{className:"space-y-4",children:[{type:"message",text:"Nova mensagem recebida",time:"2 min atr\xe1s"},{type:"appointment",text:"Consulta confirmada",time:"15 min atr\xe1s"},{type:"payment",text:"Pagamento recebido - R$ 150",time:"1 hora atr\xe1s"},{type:"reminder",text:"Lembrete enviado",time:"2 horas atr\xe1s"},{type:"appointment",text:"Nova consulta agendada",time:"3 horas atr\xe1s"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors",children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full mt-2 ${"message"===e.type?"bg-blue-500":"appointment"===e.type?"bg-green-500":"payment"===e.type?"bg-primary":"bg-yellow-500"}`}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm text-foreground",children:e.text}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.time})]})]},t))})]})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"A\xe7\xf5es R\xe1pidas"}),(0,s.jsx)(o.BT,{children:"Principais funcionalidades ao seu alcance"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)(l.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(d.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Nova Consulta"})]}),(0,s.jsxs)(l.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(f.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Cadastrar Paciente"})]}),(0,s.jsxs)(l.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(p.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Enviar Mensagem"})]}),(0,s.jsxs)(l.$,{variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,s.jsx)(h.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:"Ver Relat\xf3rios"})]})]})})]})]})})}},4735:e=>{"use strict";e.exports=require("events")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5764:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var a=r(5239),n=r(8088),s=r(8170),i=r.n(s),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,559)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6349:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},6782:(e,t,r)=>{Promise.resolve().then(r.bind(r,4672))},6870:(e,t,r)=>{Promise.resolve().then(r.bind(r,559))},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8887:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(687),n=r(3210),s=r(4780);let i=n.forwardRef(({className:e,type:t,...r},n)=>(0,a.jsx)("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));i.displayName="Input"},9727:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,437,250,318,928],()=>r(5764));module.exports=a})();