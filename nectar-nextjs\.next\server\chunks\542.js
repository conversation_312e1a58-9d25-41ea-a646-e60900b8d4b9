exports.id=542,exports.ids=[542],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var o=r(49384),s=r(82348);function a(...e){return(0,s.QP)((0,o.$)(e))}},10641:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>P});var o=r(60687),s=r(29867),a=r(43210),i=r(47313),n=r(24224),d=r(11860),u=r(4780);let c=i.Kq,l=a.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.LM,{ref:r,className:(0,u.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));l.displayName=i.LM.displayName;let f=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=a.forwardRef(({className:e,variant:t,...r},s)=>(0,o.jsx)(i.bL,{ref:s,className:(0,u.cn)(f({variant:t}),e),...r}));p.displayName=i.bL.displayName,a.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.rc,{ref:r,className:(0,u.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=i.rc.displayName;let m=a.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.bm,{ref:r,className:(0,u.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,o.jsx)(d.A,{className:"h-4 w-4"})}));m.displayName=i.bm.displayName;let g=a.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.hE,{ref:r,className:(0,u.cn)("text-sm font-semibold",e),...t}));g.displayName=i.hE.displayName;let v=a.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.VY,{ref:r,className:(0,u.cn)("text-sm opacity-90",e),...t}));function h(){let{toasts:e}=(0,s.dj)();return(0,o.jsxs)(c,{children:[e.map(function({id:e,title:t,description:r,action:s,...a}){return(0,o.jsxs)(p,{...a,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[t&&(0,o.jsx)(g,{children:t}),r&&(0,o.jsx)(v,{children:r})]}),s,(0,o.jsx)(m,{})]},e)}),(0,o.jsx)(l,{})]})}v.displayName=i.VY.displayName;var b=r(10218),x=r(52581);let y=({...e})=>{let{theme:t="system"}=(0,b.D)();return(0,o.jsx)(x.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var w=r(76242),j=r(92314),N=r(8693);function S({children:e}){let[t]=(0,a.useState)(()=>new j.E({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,o.jsx)(N.Ht,{client:t,children:e})}function P({children:e}){return(0,o.jsx)(S,{children:(0,o.jsxs)(w.Bc,{children:[e,(0,o.jsx)(h,{}),(0,o.jsx)(y,{})]})})}},13622:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\Providers.tsx","Providers")},16899:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},17579:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},21475:(e,t,r)=>{Promise.resolve().then(r.bind(r,13622))},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>u,r:()=>d});var o=r(60687),s=r(43210),a=r(8730),i=r(24224),n=r(4780);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",hero:"bg-gradient-primary text-primary-foreground hover:shadow-medium transition-all duration-300 hover:scale-105",success:"bg-success text-success-foreground hover:bg-success/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...i},u)=>{let c=s?a.DX:"button";return(0,o.jsx)(c,{className:(0,n.cn)(d({variant:t,size:r,className:e})),ref:u,...i})});u.displayName="Button"},29867:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f});var o=r(43210);let s=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],u={toasts:[]};function c(e){u=n(u,e),d.forEach(e=>{e(u)})}function l({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=o.useState(u);return o.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:l,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},35547:(e,t,r)=>{Promise.resolve().then(r.bind(r,10641))},39727:()=>{},47990:()=>{},61135:()=>{},76242:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>n,ZI:()=>c,k$:()=>u,m_:()=>d});var o=r(60687),s=r(43210),a=r(9989),i=r(4780);let n=a.Kq,d=a.bL,u=a.l9,c=s.forwardRef(({className:e,sideOffset:t=4,...r},s)=>(0,o.jsx)(a.UC,{ref:s,sideOffset:t,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));c.displayName=a.UC.displayName},79481:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var o=r(59522);function s(){return(0,o.createBrowserClient)("https://zmwdnemlzndjavlriyrc.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM")}},87979:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(43210);r(79481);var s=r(29867);function a(){let[e,t]=(0,o.useState)(null),[r,a]=(0,o.useState)(null),[i,n]=(0,o.useState)(!0),{toast:d}=(0,s.dj)();return{user:e,session:r,loading:i,signIn:async(e,t)=>{try{let r=await fetch("/api/auth/signin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),o=await r.json();if(!r.ok)return d({title:"Erro no login",description:o.error,variant:"destructive"}),{error:o.error};return{error:null}}catch(e){return d({title:"Erro no login",description:"Erro inesperado ao fazer login",variant:"destructive"}),{error:e}}},signUp:async(e,t,r)=>{try{let o=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t,name:r})}),s=await o.json();if(!o.ok)return d({title:"Erro no cadastro",description:s.error,variant:"destructive"}),{error:s.error};return d({title:"Cadastro realizado",description:s.message||"Verifique seu email para confirmar a conta"}),{error:null}}catch(e){return d({title:"Erro no cadastro",description:"Erro inesperado ao fazer cadastro",variant:"destructive"}),{error:e}}},signOut:async()=>{try{await fetch("/api/auth/signout",{method:"POST"}),d({title:"Logout realizado",description:"Voc\xea foi desconectado com sucesso"})}catch(e){console.error("Error signing out:",e)}}}}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var o=r(60687),s=r(43210),a=r(4780);let i=s.forwardRef(({className:e,type:t,...r},s)=>(0,o.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...r}));i.displayName="Input"},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>n});var o=r(37413),s=r(25091),a=r.n(s);r(61135);var i=r(13622);let n={title:"Nectar Sa\xfade - Plataforma para Profissionais da Sa\xfade",description:"Plataforma completa para profissionais da sa\xfade gerenciarem consultas, pacientes e relacionamento via WhatsApp em um s\xf3 lugar."};function d({children:e}){return(0,o.jsx)("html",{lang:"pt-BR",children:(0,o.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:(0,o.jsx)(i.Providers,{children:e})})})}}};