{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "YJe7OyuQCjzPw186m+1UYw51RjmWUlL+OPw7wyTvmcI=", "__NEXT_PREVIEW_MODE_ID": "8f80588dbaa0fcf11ca6a819d3bd1822", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "baedb5c3e155307686bc89d746afb4c9fea4cf0ebc8a72e06392eea648c5db21", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "872034a9f34b41aa20ba55d3471041e7269225293d11445e0f0237123c14aae8"}}}, "sortedMiddleware": ["/"], "functions": {}}