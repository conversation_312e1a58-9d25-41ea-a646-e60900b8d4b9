{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "QyHQeaxuUqoGbFDjcsfii", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "YJe7OyuQCjzPw186m+1UYw51RjmWUlL+OPw7wyTvmcI=", "__NEXT_PREVIEW_MODE_ID": "9133f2b75ae5ebf798edff8ac4fd2770", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a364eb5fb9c1a04558d256d02004ed78d863d29d6970969a222826f9378bbe6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cff4bf00765495ee4a239b831e6e6600d79ffd0227ec09a022ba5ef153ef826c"}}}, "functions": {}, "sortedMiddleware": ["/"]}