@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Healthcare design system - Professional and trustworthy */
    --background: 220 14% 98%;
    --foreground: 215 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 20% 15%;

    /* Medical blue - professional and trustworthy */
    --primary: 207 73% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 207 73% 65%;

    /* Clean medical green */
    --secondary: 160 35% 90%;
    --secondary-foreground: 160 70% 25%;

    --muted: 215 20% 95%;
    --muted-foreground: 215 15% 45%;

    /* Warm accent for friendliness */
    --accent: 25 85% 95%;
    --accent-foreground: 25 85% 35%;

    --destructive: 0 70% 55%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 20% 90%;
    --input: 215 20% 95%;
    --ring: 207 73% 45%;

    /* Healthcare specific colors */
    --success: 160 70% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 85% 55%;
    --warning-foreground: 0 0% 100%;
    --info: 207 73% 55%;
    --info-foreground: 0 0% 100%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-glow)) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));

    /* Shadows */
    --shadow-soft: 0 2px 8px -2px hsl(var(--primary) / 0.1);
    --shadow-medium: 0 4px 16px -4px hsl(var(--primary) / 0.15);
    --shadow-strong: 0 8px 32px -8px hsl(var(--primary) / 0.2);

    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}