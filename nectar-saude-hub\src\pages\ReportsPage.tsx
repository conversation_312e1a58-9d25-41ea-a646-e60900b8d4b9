import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart3, TrendingUp, Users, Calendar, DollarSign, MessageSquare, Activity } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useSupabase } from '@/hooks/useSupabase';
import { format, startOfMonth, endOfMonth, subMonths, startOfWeek, endOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';

type ReportData = {
  totalPatients: number;
  totalAppointments: number;
  totalRevenue: number;
  totalMessages: number;
  appointmentsByStatus: { [key: string]: number };
  appointmentsByType: { [key: string]: number };
  monthlyRevenue: { month: string; revenue: number }[];
  weeklyAppointments: { week: string; count: number }[];
};

const ReportsPage = () => {
  const [reportData, setReportData] = useState<ReportData>({
    totalPatients: 0,
    totalAppointments: 0,
    totalRevenue: 0,
    totalMessages: 0,
    appointmentsByStatus: {},
    appointmentsByType: {},
    monthlyRevenue: [],
    weeklyAppointments: []
  });
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('month');
  const { user } = useSupabase();

  useEffect(() => {
    if (user) {
      fetchReportData();
    }
  }, [user, period]);

  const fetchReportData = async () => {
    if (!user) return;
    
    const now = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (period) {
      case 'week':
        startDate = startOfWeek(now);
        endDate = endOfWeek(now);
        break;
      case 'month':
        startDate = startOfMonth(now);
        endDate = endOfMonth(now);
        break;
      case '3months':
        startDate = startOfMonth(subMonths(now, 2));
        endDate = endOfMonth(now);
        break;
      default:
        startDate = startOfMonth(now);
        endDate = endOfMonth(now);
    }

    try {
      // Fetch patients count
      const { count: patientsCount } = await supabase
        .from('patients')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      // Fetch appointments data
      const { data: appointments, count: appointmentsCount } = await supabase
        .from('appointments')
        .select('*', { count: 'exact' })
        .eq('user_id', user.id)
        .gte('start_time', startDate.toISOString())
        .lte('start_time', endDate.toISOString());

      // Fetch messages count
      const { count: messagesCount } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      // Calculate revenue
      const totalRevenue = appointments?.reduce((sum, apt) => sum + (apt.price || 0), 0) || 0;

      // Group appointments by status
      const appointmentsByStatus = appointments?.reduce((acc: any, apt) => {
        acc[apt.status] = (acc[apt.status] || 0) + 1;
        return acc;
      }, {}) || {};

      // Group appointments by type
      const appointmentsByType = appointments?.reduce((acc: any, apt) => {
        acc[apt.type] = (acc[apt.type] || 0) + 1;
        return acc;
      }, {}) || {};

      // Monthly revenue for the last 6 months
      const monthlyRevenue = [];
      for (let i = 5; i >= 0; i--) {
        const monthStart = startOfMonth(subMonths(now, i));
        const monthEnd = endOfMonth(subMonths(now, i));
        
        const { data: monthAppointments } = await supabase
          .from('appointments')
          .select('price')
          .eq('user_id', user.id)
          .gte('start_time', monthStart.toISOString())
          .lte('start_time', monthEnd.toISOString());

        const monthRevenue = monthAppointments?.reduce((sum, apt) => sum + (apt.price || 0), 0) || 0;
        
        monthlyRevenue.push({
          month: format(monthStart, 'MMM/yy', { locale: ptBR }),
          revenue: monthRevenue
        });
      }

      // Weekly appointments for the last 4 weeks
      const weeklyAppointments = [];
      for (let i = 3; i >= 0; i--) {
        const weekStart = startOfWeek(subMonths(now, 0, { weekStartsOn: 0 }));
        const weekEnd = endOfWeek(subMonths(now, 0, { weekStartsOn: 0 }));
        
        const { count: weekCount } = await supabase
          .from('appointments')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id)
          .gte('start_time', weekStart.toISOString())
          .lte('start_time', weekEnd.toISOString());

        weeklyAppointments.push({
          week: `Sem ${i + 1}`,
          count: weekCount || 0
        });
      }

      setReportData({
        totalPatients: patientsCount || 0,
        totalAppointments: appointmentsCount || 0,
        totalRevenue,
        totalMessages: messagesCount || 0,
        appointmentsByStatus,
        appointmentsByType,
        monthlyRevenue,
        weeklyAppointments
      });
    } catch (error) {
      console.error('Error fetching report data:', error);
    }
    
    setLoading(false);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Relatórios</h1>
          <p className="text-muted-foreground">Análise de desempenho e métricas</p>
        </div>
        
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-[180px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="week">Esta semana</SelectItem>
            <SelectItem value="month">Este mês</SelectItem>
            <SelectItem value="3months">Últimos 3 meses</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Pacientes</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{reportData.totalPatients}</div>
            <p className="text-xs text-muted-foreground">Pacientes cadastrados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Consultas</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{reportData.totalAppointments}</div>
            <p className="text-xs text-muted-foreground">No período selecionado</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Receita</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{formatCurrency(reportData.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">No período selecionado</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensagens</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-info">{reportData.totalMessages}</div>
            <p className="text-xs text-muted-foreground">No período selecionado</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="appointments" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="appointments">Consultas</TabsTrigger>
          <TabsTrigger value="revenue">Receita</TabsTrigger>
          <TabsTrigger value="patients">Pacientes</TabsTrigger>
          <TabsTrigger value="engagement">Engajamento</TabsTrigger>
        </TabsList>
        
        <TabsContent value="appointments" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="mr-2 h-5 w-5 text-primary" />
                  Status das Consultas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(reportData.appointmentsByStatus).map(([status, count]) => (
                    <div key={status} className="flex items-center justify-between">
                      <span className="capitalize">
                        {status === 'scheduled' && 'Agendadas'}
                        {status === 'confirmed' && 'Confirmadas'}  
                        {status === 'completed' && 'Concluídas'}
                        {status === 'cancelled' && 'Canceladas'}
                      </span>
                      <div className="flex items-center">
                        <div className="w-20 bg-muted rounded-full h-2 mr-2">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${(count / Math.max(...Object.values(reportData.appointmentsByStatus))) * 100}%` }}
                          ></div>
                        </div>
                        <span className="font-medium">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="mr-2 h-5 w-5 text-primary" />
                  Tipos de Consulta
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(reportData.appointmentsByType).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between">
                      <span className="capitalize">
                        {type === 'consultation' && 'Consultas'}
                        {type === 'return' && 'Retornos'}
                        {type === 'teleconsultation' && 'Teleconsultas'}
                      </span>
                      <div className="flex items-center">
                        <div className="w-20 bg-muted rounded-full h-2 mr-2">
                          <div 
                            className="bg-success h-2 rounded-full" 
                            style={{ width: `${(count / Math.max(...Object.values(reportData.appointmentsByType))) * 100}%` }}
                          ></div>
                        </div>
                        <span className="font-medium">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="mr-2 h-5 w-5 text-primary" />
                Receita dos Últimos 6 Meses
              </CardTitle>
              <CardDescription>
                Evolução da receita mensal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.monthlyRevenue.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span>{item.month}</span>
                    <div className="flex items-center">
                      <div className="w-32 bg-muted rounded-full h-2 mr-4">
                        <div 
                          className="bg-success h-2 rounded-full" 
                          style={{ 
                            width: `${Math.max(...reportData.monthlyRevenue.map(r => r.revenue)) > 0 
                              ? (item.revenue / Math.max(...reportData.monthlyRevenue.map(r => r.revenue))) * 100 
                              : 0}%` 
                          }}
                        ></div>
                      </div>
                      <span className="font-medium text-success">{formatCurrency(item.revenue)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="patients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5 text-primary" />
                Resumo de Pacientes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="mx-auto h-16 w-16 text-primary mb-4" />
                <h3 className="text-2xl font-bold text-foreground mb-2">
                  {reportData.totalPatients} Pacientes
                </h3>
                <p className="text-muted-foreground">
                  Total de pacientes cadastrados na plataforma
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="mr-2 h-5 w-5 text-primary" />
                Engajamento por Mensagens
              </CardTitle>
              <CardDescription>
                Interações com pacientes no período
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <MessageSquare className="mx-auto h-16 w-16 text-info mb-4" />
                <h3 className="text-2xl font-bold text-foreground mb-2">
                  {reportData.totalMessages} Mensagens
                </h3>
                <p className="text-muted-foreground">
                  Total de mensagens trocadas no período selecionado
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReportsPage;