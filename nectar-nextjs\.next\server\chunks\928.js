exports.id=928,exports.ids=[928],exports.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},641:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>S});var s=r(687),o=r(9867),a=r(3210),i=r(7313),n=r(4224),d=r(1860),l=r(4780);let c=i.Kq,u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.LM,{ref:r,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=i.LM.displayName;let f=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),m=a.forwardRef(({className:e,variant:t,...r},o)=>(0,s.jsx)(i.bL,{ref:o,className:(0,l.cn)(f({variant:t}),e),...r}));m.displayName=i.bL.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.rc,{ref:r,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=i.rc.displayName;let p=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.bm,{ref:r,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}));p.displayName=i.bm.displayName;let v=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.hE,{ref:r,className:(0,l.cn)("text-sm font-semibold",e),...t}));v.displayName=i.hE.displayName;let g=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.VY,{ref:r,className:(0,l.cn)("text-sm opacity-90",e),...t}));function x(){let{toasts:e}=(0,o.dj)();return(0,s.jsxs)(c,{children:[e.map(function({id:e,title:t,description:r,action:o,...a}){return(0,s.jsxs)(m,{...a,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&(0,s.jsx)(v,{children:t}),r&&(0,s.jsx)(g,{children:r})]}),o,(0,s.jsx)(p,{})]},e)}),(0,s.jsx)(u,{})]})}g.displayName=i.VY.displayName;var h=r(218),b=r(2581);let y=({...e})=>{let{theme:t="system"}=(0,h.D)();return(0,s.jsx)(b.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var w=r(6242),N=r(2314),j=r(8693);function P({children:e}){let[t]=(0,a.useState)(()=>new N.E({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,s.jsx)(j.Ht,{client:t,children:e})}function S({children:e}){return(0,s.jsx)(P,{children:(0,s.jsxs)(w.Bc,{children:[e,(0,s.jsx)(x,{}),(0,s.jsx)(y,{})]})})}},1135:()=>{},1475:(e,t,r)=>{Promise.resolve().then(r.bind(r,3622))},3622:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\Providers.tsx","Providers")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>n});var s=r(7413),o=r(5091),a=r.n(o);r(1135);var i=r(3622);let n={title:"Nectar Sa\xfade - Plataforma para Profissionais da Sa\xfade",description:"Plataforma completa para profissionais da sa\xfade gerenciarem consultas, pacientes e relacionamento via WhatsApp em um s\xf3 lugar."};function d({children:e}){return(0,s.jsx)("html",{lang:"pt-BR",children:(0,s.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:(0,s.jsx)(i.Providers,{children:e})})})}},4493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var s=r(687),o=r(3210),a=r(4780);let i=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let n=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let d=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let l=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let c=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(9384),o=r(2348);function a(...e){return(0,o.QP)((0,s.$)(e))}},5547:(e,t,r)=>{Promise.resolve().then(r.bind(r,641))},6242:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>n,ZI:()=>c,k$:()=>l,m_:()=>d});var s=r(687),o=r(3210),a=r(592),i=r(4780);let n=a.Kq,d=a.bL,l=a.l9,c=o.forwardRef(({className:e,sideOffset:t=4,...r},o)=>(0,s.jsx)(a.UC,{ref:o,sideOffset:t,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));c.displayName=a.UC.displayName},6899:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},7579:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(687),o=r(3210),a=r(8730),i=r(4224),n=r(4780);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",hero:"bg-gradient-primary text-primary-foreground hover:shadow-medium transition-all duration-300 hover:scale-105",success:"bg-success text-success-foreground hover:bg-success/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...i},l)=>{let c=o?a.DX:"button";return(0,s.jsx)(c,{className:(0,n.cn)(d({variant:t,size:r,className:e})),ref:l,...i})});l.displayName="Button"},9867:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f});var s=r(3210);let o=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function c(e){l=n(l,e),d.forEach(e=>{e(l)})}function u({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=s.useState(l);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}};