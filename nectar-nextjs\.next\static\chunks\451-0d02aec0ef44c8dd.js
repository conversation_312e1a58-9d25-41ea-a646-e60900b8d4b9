"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[451],{1764:(e,t,n)=>{n.d(t,{UC:()=>tD,Kq:()=>tO,bL:()=>tk,l9:()=>tS});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(9178),s=n(1285);let u=["top","right","bottom","left"],c=Math.min,f=Math.max,d=Math.round,p=Math.floor,h=e=>({x:e,y:e}),m={left:"right",right:"left",bottom:"top",top:"bottom"},g={start:"end",end:"start"};function v(e,t){return"function"==typeof e?e(t):e}function y(e){return e.split("-")[0]}function w(e){return e.split("-")[1]}function x(e){return"x"===e?"y":"x"}function b(e){return"y"===e?"height":"width"}let E=new Set(["top","bottom"]);function C(e){return E.has(y(e))?"y":"x"}function R(e){return e.replace(/start|end/g,e=>g[e])}let T=["left","right"],L=["right","left"],A=["top","bottom"],P=["bottom","top"];function O(e){return e.replace(/left|right|bottom|top/g,e=>m[e])}function k(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function D(e,t,n){let r,{reference:o,floating:i}=e,l=C(t),a=x(C(t)),s=b(a),u=y(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(w(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let j=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=D(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:v,data:y,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:f}=D(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function H(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=v(t,e),h=k(p),m=a[d?"floating"===f?"reference":"floating":f],g=S(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),x=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=S(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:s}):y);return{top:(g.top-b.top+h.top)/x.y,bottom:(b.bottom-g.bottom+h.bottom)/x.y,left:(g.left-b.left+h.left)/x.x,right:(b.right-g.right+h.right)/x.x}}function N(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function M(e){return u.some(t=>e[t]>=0)}let W=new Set(["left","top"]);async function F(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=y(n),a=w(n),s="y"===C(n),u=W.has(l)?-1:1,c=i&&s?-1:1,f=v(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:d*u}:{x:d*u,y:p*c}}function B(){return"undefined"!=typeof window}function z(e){return V(e)?(e.nodeName||"").toLowerCase():"#document"}function _(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(V(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function V(e){return!!B()&&(e instanceof Node||e instanceof _(e).Node)}function G(e){return!!B()&&(e instanceof Element||e instanceof _(e).Element)}function Y(e){return!!B()&&(e instanceof HTMLElement||e instanceof _(e).HTMLElement)}function X(e){return!!B()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof _(e).ShadowRoot)}let q=new Set(["inline","contents"]);function $(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ei(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!q.has(o)}let K=new Set(["table","td","th"]),U=[":popover-open",":modal"];function Z(e){return U.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let J=["transform","translate","scale","rotate","perspective"],Q=["transform","translate","scale","rotate","perspective","filter"],ee=["paint","layout","strict","content"];function et(e){let t=en(),n=G(e)?ei(e):e;return J.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||Q.some(e=>(n.willChange||"").includes(e))||ee.some(e=>(n.contain||"").includes(e))}function en(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let er=new Set(["html","body","#document"]);function eo(e){return er.has(z(e))}function ei(e){return _(e).getComputedStyle(e)}function el(e){return G(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ea(e){if("html"===z(e))return e;let t=e.assignedSlot||e.parentNode||X(e)&&e.host||I(e);return X(t)?t.host:t}function es(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ea(t);return eo(n)?t.ownerDocument?t.ownerDocument.body:t.body:Y(n)&&$(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=_(o);if(i){let e=eu(l);return t.concat(l,l.visualViewport||[],$(o)?o:[],e&&n?es(e):[])}return t.concat(o,es(o,[],n))}function eu(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ec(e){let t=ei(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=Y(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=d(n)!==i||d(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function ef(e){return G(e)?e:e.contextElement}function ed(e){let t=ef(e);if(!Y(t))return h(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ec(t),l=(i?d(n.width):n.width)/r,a=(i?d(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ep=h(0);function eh(e){let t=_(e);return en()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ep}function em(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=ef(e),a=h(1);t&&(r?G(r)&&(a=ed(r)):a=ed(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===_(l))&&o)?eh(l):h(0),u=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=_(l),t=r&&G(r)?_(r):r,n=e,o=eu(n);for(;o&&r&&t!==n;){let e=ed(o),t=o.getBoundingClientRect(),r=ei(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=i,c+=l,o=eu(n=_(o))}}return S({width:f,height:d,x:u,y:c})}function eg(e,t){let n=el(e).scrollLeft;return t?t.left+n:em(I(e)).left+n}function ev(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eg(e,r)),y:r.top+t.scrollTop}}let ey=new Set(["absolute","fixed"]);function ew(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=_(e),r=I(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=en();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=I(e),n=el(e),r=e.ownerDocument.body,o=f(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=f(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eg(e),a=-n.scrollTop;return"rtl"===ei(r).direction&&(l+=f(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(I(e));else if(G(t))r=function(e,t){let n=em(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Y(e)?ed(e):h(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eh(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return S(r)}function ex(e){return"static"===ei(e).position}function eb(e,t){if(!Y(e)||"fixed"===ei(e).position)return null;if(t)return t(e);let n=e.offsetParent;return I(e)===n&&(n=n.ownerDocument.body),n}function eE(e,t){var n;let r=_(e);if(Z(e))return r;if(!Y(e)){let t=ea(e);for(;t&&!eo(t);){if(G(t)&&!ex(t))return t;t=ea(t)}return r}let o=eb(e,t);for(;o&&(n=o,K.has(z(n)))&&ex(o);)o=eb(o,t);return o&&eo(o)&&ex(o)&&!et(o)?r:o||function(e){let t=ea(e);for(;Y(t)&&!eo(t);){if(et(t))return t;if(Z(t))break;t=ea(t)}return null}(e)||r}let eC=async function(e){let t=this.getOffsetParent||eE,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=Y(t),o=I(t),i="fixed"===n,l=em(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=h(0);if(r||!r&&!i)if(("body"!==z(t)||$(o))&&(a=el(t)),r){let e=em(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eg(o));i&&!r&&o&&(s.x=eg(o));let u=!o||r||i?h(0):ev(o,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eR={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=I(r),a=!!t&&Z(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},u=h(1),c=h(0),f=Y(r);if((f||!f&&!i)&&(("body"!==z(r)||$(l))&&(s=el(r)),Y(r))){let e=em(r);u=ed(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!l||f||i?h(0):ev(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?Z(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=es(e,[],!1).filter(e=>G(e)&&"body"!==z(e)),o=null,i="fixed"===ei(e).position,l=i?ea(e):e;for(;G(l)&&!eo(l);){let t=ei(l),n=et(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ey.has(o.position)||$(l)&&!n&&function e(t,n){let r=ea(t);return!(r===n||!G(r)||eo(r))&&("fixed"===ei(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ea(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=ew(t,n,o);return e.top=f(r.top,e.top),e.right=c(r.right,e.right),e.bottom=c(r.bottom,e.bottom),e.left=f(r.left,e.left),e},ew(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eE,getElementRects:eC,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ec(e);return{width:t,height:n}},getScale:ed,isElement:G,isRTL:function(e){return"rtl"===ei(e).direction}};function eT(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eL=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:d=0}=v(e,t)||{};if(null==u)return{};let p=k(d),h={x:n,y:r},m=x(C(o)),g=b(m),y=await l.getDimensions(u),E="y"===m,R=E?"clientHeight":"clientWidth",T=i.reference[g]+i.reference[m]-h[m]-i.floating[g],L=h[m]-i.reference[m],A=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),P=A?A[R]:0;P&&await (null==l.isElement?void 0:l.isElement(A))||(P=a.floating[R]||i.floating[g]);let O=P/2-y[g]/2-1,S=c(p[E?"top":"left"],O),D=c(p[E?"bottom":"right"],O),j=P-y[g]-D,H=P/2-y[g]/2+(T/2-L/2),N=f(S,c(H,j)),M=!s.arrow&&null!=w(o)&&H!==N&&i.reference[g]/2-(H<S?S:D)-y[g]/2<0,W=M?H<S?H-S:H-j:0;return{[m]:h[m]+W,data:{[m]:N,centerOffset:H-N-W,...M&&{alignmentOffset:W}},reset:M}}}),eA=(e,t,n)=>{let r=new Map,o={platform:eR,...n},i={...o.platform,_c:r};return j(e,t,{...o,platform:i})};var eP=n(7650),eO="undefined"!=typeof document?r.useLayoutEffect:function(){};function ek(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ek(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ek(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eD(e,t){let n=eS(e);return Math.round(t*n)/n}function ej(e){let t=r.useRef(e);return eO(()=>{t.current=e}),t}let eH=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eL({element:n.current,padding:r}).fn(t):{}:n?eL({element:n,padding:r}).fn(t):{}}}),eN=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await F(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eM=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=v(e,t),u={x:n,y:r},d=await H(t,s),p=C(y(o)),h=x(p),m=u[h],g=u[p];if(i){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=m+d[e],r=m-d[t];m=f(n,c(m,r))}if(l){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=g+d[e],r=g-d[t];g=f(n,c(g,r))}let w=a.fn({...t,[h]:m,[p]:g});return{...w,data:{x:w.x-n,y:w.y-r,enabled:{[h]:i,[p]:l}}}}}}(e),options:[e,t]}),eW=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=v(e,t),c={x:n,y:r},f=C(o),d=x(f),p=c[d],h=c[f],m=v(a,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,n=i.reference[d]+i.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var w,b;let e="y"===d?"width":"height",t=W.has(y(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(w=l.offset)?void 0:w[f])||0)+(t?0:g.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[f])||0)-(t?g.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}),eF=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:k=!0,...S}=v(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let D=y(a),j=C(c),N=y(c)===c,M=await (null==f.isRTL?void 0:f.isRTL(d.floating)),W=m||(N||!k?[O(c)]:function(e){let t=O(e);return[R(e),t,R(t)]}(c)),F="none"!==E;!m&&F&&W.push(...function(e,t,n,r){let o=w(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?L:T;return t?T:L;case"left":case"right":return t?A:P;default:return[]}}(y(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(R)))),i}(c,k,E,M));let B=[c,...W],z=await H(t,S),_=[],I=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&_.push(z[D]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=w(e),o=x(C(e)),i=b(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=O(l)),[l,O(l)]}(a,u,M);_.push(z[e[0]],z[e[1]])}if(I=[...I,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==h||j===C(t)||I.every(e=>e.overflows[0]>0&&C(e.placement)===j)))return{data:{index:e,overflows:I},reset:{placement:t}};let n=null==(i=I.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(g){case"bestFit":{let e=null==(l=I.filter(e=>{if(F){let t=C(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eB=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:s,elements:u}=t,{apply:d=()=>{},...p}=v(e,t),h=await H(t,p),m=y(l),g=w(l),x="y"===C(l),{width:b,height:E}=a.floating;"top"===m||"bottom"===m?(o=m,i=g===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=m,o="end"===g?"top":"bottom");let R=E-h.top-h.bottom,T=b-h.left-h.right,L=c(E-h[o],R),A=c(b-h[i],T),P=!t.middlewareData.shift,O=L,k=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(O=R),P&&!g){let e=f(h.left,0),t=f(h.right,0),n=f(h.top,0),r=f(h.bottom,0);x?k=b-2*(0!==e||0!==t?e+t:f(h.left,h.right)):O=E-2*(0!==n||0!==r?n+r:f(h.top,h.bottom))}await d({...t,availableWidth:k,availableHeight:O});let S=await s.getDimensions(u.floating);return b!==S.width||E!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ez=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=v(e,t);switch(r){case"referenceHidden":{let e=N(await H(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:M(e)}}}case"escaped":{let e=N(await H(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:M(e)}}}default:return{}}}}}(e),options:[e,t]}),e_=(e,t)=>({...eH(e),options:[e,t]});var eI=n(3655),eV=n(5155),eG=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eV.jsx)(eI.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eV.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eG.displayName="Arrow";var eY=n(9033),eX=n(2712),eq="Popper",[e$,eK]=(0,l.A)(eq),[eU,eZ]=e$(eq),eJ=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eV.jsx)(eU,{scope:t,anchor:o,onAnchorChange:i,children:n})};eJ.displayName=eq;var eQ="PopperAnchor",e0=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...l}=e,a=eZ(eQ,n),s=r.useRef(null),u=(0,i.s)(t,s);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||s.current)}),o?null:(0,eV.jsx)(eI.sG.div,{...l,ref:u})});e0.displayName=eQ;var e1="PopperContent",[e5,e2]=e$(e1),e6=r.forwardRef((e,t)=>{var n,o,l,a,s,u,d,h;let{__scopePopper:m,side:g="bottom",sideOffset:v=0,align:y="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:b=!0,collisionBoundary:E=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:T=!1,updatePositionStrategy:L="optimized",onPlaced:A,...P}=e,O=eZ(e1,m),[k,S]=r.useState(null),D=(0,i.s)(t,e=>S(e)),[j,H]=r.useState(null),N=function(e){let[t,n]=r.useState(void 0);return(0,eX.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),M=null!=(d=null==N?void 0:N.width)?d:0,W=null!=(h=null==N?void 0:N.height)?h:0,F="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},B=Array.isArray(E)?E:[E],z=B.length>0,_={padding:F,boundary:B.filter(e9),altBoundary:z},{refs:V,floatingStyles:G,placement:Y,isPositioned:X,middlewareData:q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ek(p,o)||h(o);let[m,g]=r.useState(null),[v,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=l||m,E=a||v,C=r.useRef(null),R=r.useRef(null),T=r.useRef(f),L=null!=u,A=ej(u),P=ej(i),O=ej(c),k=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};P.current&&(e.platform=P.current),eA(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};S.current&&!ek(T.current,t)&&(T.current=t,eP.flushSync(()=>{d(t)}))})},[p,t,n,P,O]);eO(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let S=r.useRef(!1);eO(()=>(S.current=!0,()=>{S.current=!1}),[]),eO(()=>{if(b&&(C.current=b),E&&(R.current=E),b&&E){if(A.current)return A.current(b,E,k);k()}},[b,E,k,A,L]);let D=r.useMemo(()=>({reference:C,floating:R,setReference:w,setFloating:x}),[w,x]),j=r.useMemo(()=>({reference:b,floating:E}),[b,E]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eD(j.floating,f.x),r=eD(j.floating,f.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eS(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,j.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:k,refs:D,elements:j,floatingStyles:H}),[f,k,D,j,H])}({strategy:"fixed",placement:g+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,d=ef(e),h=i||l?[...d?es(d):[],...es(t)]:[];h.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let m=d&&s?function(e,t){let n,r=null,o=I(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:d,top:h,width:m,height:g}=u;if(a||t(),!m||!g)return;let v=p(h),y=p(o.clientWidth-(d+m)),w={rootMargin:-v+"px "+-y+"px "+-p(o.clientHeight-(h+g))+"px "+-p(d)+"px",threshold:f(0,c(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eT(u,e.getBoundingClientRect())||l(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),i}(d,n):null,g=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===d&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),d&&!u&&v.observe(d),v.observe(t));let y=u?em(e):null;return u&&function t(){let r=em(e);y&&!eT(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===L})},elements:{reference:O.anchor},middleware:[eN({mainAxis:v+W,alignmentAxis:w}),b&&eM({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eW():void 0,..._}),b&&eF({..._}),eB({..._,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&e_({element:j,padding:x}),e4({arrowWidth:M,arrowHeight:W}),T&&ez({strategy:"referenceHidden",..._})]}),[$,K]=te(Y),U=(0,eY.c)(A);(0,eX.N)(()=>{X&&(null==U||U())},[X,U]);let Z=null==(n=q.arrow)?void 0:n.x,J=null==(o=q.arrow)?void 0:o.y,Q=(null==(l=q.arrow)?void 0:l.centerOffset)!==0,[ee,et]=r.useState();return(0,eX.N)(()=>{k&&et(window.getComputedStyle(k).zIndex)},[k]),(0,eV.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:X?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[null==(a=q.transformOrigin)?void 0:a.x,null==(s=q.transformOrigin)?void 0:s.y].join(" "),...(null==(u=q.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eV.jsx)(e5,{scope:m,placedSide:$,onArrowChange:H,arrowX:Z,arrowY:J,shouldHideArrow:Q,children:(0,eV.jsx)(eI.sG.div,{"data-side":$,"data-align":K,...P,ref:D,style:{...P.style,animation:X?void 0:"none"}})})})});e6.displayName=e1;var e8="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e7=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e2(e8,n),i=e3[o.placedSide];return(0,eV.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eV.jsx)(eG,{...r,ref:t,style:{...r.style,display:"block"}})})});function e9(e){return null!==e}e7.displayName=e8;var e4=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=te(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(i=null==(r=u.arrow)?void 0:r.x)?i:0)+f/2,v=(null!=(l=null==(o=u.arrow)?void 0:o.y)?l:0)+d/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(g,"px"),w="".concat(-d,"px")):"top"===p?(y=c?m:"".concat(g,"px"),w="".concat(s.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=c?m:"".concat(v,"px")):"left"===p&&(y="".concat(s.floating.width+d,"px"),w=c?m:"".concat(v,"px")),{data:{x:y,y:w}}}});function te(e){let[t,n="center"]=e.split("-");return[t,n]}n(4378);var tt=n(8905),tn=n(9708),tr=n(5845),to=n(2564),[ti,tl]=(0,l.A)("Tooltip",[eK]),ta=eK(),ts="TooltipProvider",tu="tooltip.open",[tc,tf]=ti(ts),td=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=r.useRef(!0),s=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,eV.jsx)(tc,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};td.displayName=ts;var tp="Tooltip",[th,tm]=ti(tp),tg=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:u}=e,c=tf(tp,e.__scopeTooltip),f=ta(t),[d,p]=r.useState(null),h=(0,s.B)(),m=r.useRef(0),g=null!=a?a:c.disableHoverableContent,v=null!=u?u:c.delayDuration,y=r.useRef(!1),[w,x]=(0,tr.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(tu))):c.onClose(),null==l||l(e)},caller:tp}),b=r.useMemo(()=>w?y.current?"delayed-open":"instant-open":"closed",[w]),E=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y.current=!1,x(!0)},[x]),C=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,x(!1)},[x]),R=r.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{y.current=!0,x(!0),m.current=0},v)},[v,x]);return r.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,eV.jsx)(eJ,{...f,children:(0,eV.jsx)(th,{scope:t,contentId:h,open:w,stateAttribute:b,trigger:d,onTriggerChange:p,onTriggerEnter:r.useCallback(()=>{c.isOpenDelayedRef.current?R():E()},[c.isOpenDelayedRef,R,E]),onTriggerLeave:r.useCallback(()=>{g?C():(window.clearTimeout(m.current),m.current=0)},[C,g]),onOpen:E,onClose:C,disableHoverableContent:g,children:n})})};tg.displayName=tp;var tv="TooltipTrigger",ty=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=tm(tv,n),s=tf(tv,n),u=ta(n),c=r.useRef(null),f=(0,i.s)(t,c,a.onTriggerChange),d=r.useRef(!1),p=r.useRef(!1),h=r.useCallback(()=>d.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,eV.jsx)(e0,{asChild:!0,...u,children:(0,eV.jsx)(eI.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(p.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),p.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),p.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),d.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{d.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});ty.displayName=tv;var[tw,tx]=ti("TooltipPortal",{forceMount:void 0}),tb="TooltipContent",tE=r.forwardRef((e,t)=>{let n=tx(tb,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=tm(tb,e.__scopeTooltip);return(0,eV.jsx)(tt.C,{present:r||l.open,children:l.disableHoverableContent?(0,eV.jsx)(tA,{side:o,...i,ref:t}):(0,eV.jsx)(tC,{side:o,...i,ref:t})})}),tC=r.forwardRef((e,t)=>{let n=tm(tb,e.__scopeTooltip),o=tf(tb,e.__scopeTooltip),l=r.useRef(null),a=(0,i.s)(t,l),[s,u]=r.useState(null),{trigger:c,onClose:f}=n,d=l.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{u(null),p(!1)},[p]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&d){let e=e=>m(e,d),t=e=>m(e,c);return c.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[c,d,m,h]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==d?void 0:d.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,f=a.y;u>r!=f>r&&n<(c-s)*(r-u)/(f-u)+s&&(o=!o)}return o}(n,s);r?h():o&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,d,s,f,h]),(0,eV.jsx)(tA,{...e,ref:a})}),[tR,tT]=ti(tp,{isInside:!1}),tL=(0,tn.Dc)("TooltipContent"),tA=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...u}=e,c=tm(tb,n),f=ta(n),{onClose:d}=c;return r.useEffect(()=>(document.addEventListener(tu,d),()=>document.removeEventListener(tu,d)),[d]),r.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&d()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,d]),(0,eV.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:d,children:(0,eV.jsxs)(e6,{"data-state":c.stateAttribute,...f,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eV.jsx)(tL,{children:o}),(0,eV.jsx)(tR,{scope:n,isInside:!0,children:(0,eV.jsx)(to.bL,{id:c.contentId,role:"tooltip",children:i||o})})]})})});tE.displayName=tb;var tP="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=ta(n);return tT(tP,n).isInside?null:(0,eV.jsx)(e7,{...o,...r,ref:t})}).displayName=tP;var tO=td,tk=tg,tS=ty,tD=tE},2564:(e,t,n)=>{n.d(t,{bL:()=>s,s6:()=>a});var r=n(2115),o=n(3655),i=n(5155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a},4378:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(2115),o=n(7650),i=n(3655),l=n(2712),a=n(5155),s=r.forwardRef((e,t)=>{var n,s;let{container:u,...c}=e,[f,d]=r.useState(!1);(0,l.N)(()=>d(!0),[]);let p=u||f&&(null==(s=globalThis)||null==(n=s.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...c,ref:t}),p):null});s.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9178:(e,t,n)=>{n.d(t,{lg:()=>v,qW:()=>d,bL:()=>g});var r,o=n(2115),i=n(5185),l=n(3655),a=n(6101),s=n(9033),u=n(5155),c="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,E=o.useContext(f),[C,R]=o.useState(null),T=null!=(d=null==C?void 0:C.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,L]=o.useState({}),A=(0,a.s)(t,e=>R(e)),P=Array.from(E.layers),[O]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),k=P.indexOf(O),S=C?P.indexOf(C):-1,D=E.layersWithOutsidePointerEventsDisabled.size>0,j=S>=k,H=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));j&&!n&&(null==v||v(e),null==w||w(e),e.defaultPrevented||null==x||x())},T),N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},T);return!function(e,t=globalThis?.document){let n=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S===E.layers.size-1&&(null==g||g(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},T),o.useEffect(()=>{if(C)return p&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),h(),()=>{p&&1===E.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=r)}},[C,T,p,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),h())},[C,E]),o.useEffect(()=>{let e=()=>L({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(l.sG.div,{...b,ref:A,style:{pointerEvents:D?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,H.onPointerDownCapture)})});d.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(l.sG.div,{...e,ref:i})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var g=d,v=p}}]);