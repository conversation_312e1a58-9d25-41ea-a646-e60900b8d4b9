import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesUpdate } from '@/types/supabase'

type Appointment = Tables<'appointments'>
type AppointmentUpdate = TablesUpdate<'appointments'>

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const { data: appointment, error } = await supabase
        .from('appointments')
        .select(`
          *,
          patients!inner(name)
        `)
        .eq('id', id)
        .eq('user_id', userId)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(appointment)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const updateData: AppointmentUpdate = {
        ...body,
        price: body.price ? parseFloat(body.price) : null,
      }

      if (body.start_time) {
        updateData.start_time = new Date(body.start_time).toISOString()
      }
      
      if (body.end_time) {
        updateData.end_time = new Date(body.end_time).toISOString()
      }

      const { data: appointment, error } = await supabase
        .from('appointments')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', userId)
        .select(`
          *,
          patients!inner(name)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(appointment)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', id)
        .eq('user_id', userId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ success: true })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
