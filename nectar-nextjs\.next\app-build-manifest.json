{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/dashboard/configuracoes/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/configuracoes/page.js"], "/dashboard/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/page.js"], "/api/dashboard/stats/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/dashboard/stats/route.js"], "/dashboard/agenda/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/agenda/page.js"], "/api/appointments/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/appointments/route.js"], "/api/patients/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/patients/route.js"], "/dashboard/pacientes/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/pacientes/page.js"], "/dashboard/mensagens/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/mensagens/page.js"], "/dashboard/campanhas/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/campanhas/page.js"], "/api/campaigns/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/campaigns/route.js"], "/api/auth/signout/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/signout/route.js"], "/auth/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/auth/page.js"], "/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/api/auth/signin/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/signin/route.js"], "/_not-found/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/_not-found/page.js"]}}