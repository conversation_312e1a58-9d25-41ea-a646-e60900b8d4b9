"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Settings, User, Bell, Shield, Smartphone, Mail } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

type UserProfile = {
  name: string;
  email: string;
  phone: string;
  specialty: string;
  crm: string;
  clinic_name: string;
  clinic_address: string;
};

type NotificationSettings = {
  email_appointments: boolean;
  email_messages: boolean;
  sms_appointments: boolean;
  sms_messages: boolean;
  whatsapp_notifications: boolean;
};

type IntegrationSettings = {
  whatsapp_token: string;
  whatsapp_phone: string;
  email_smtp_host: string;
  email_smtp_port: string;
  email_smtp_user: string;
  email_smtp_password: string;
};

const SettingsPage = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  const [profile, setProfile] = useState<UserProfile>({
    name: '',
    email: '',
    phone: '',
    specialty: '',
    crm: '',
    clinic_name: '',
    clinic_address: ''
  });

  const [notifications, setNotifications] = useState<NotificationSettings>({
    email_appointments: true,
    email_messages: true,
    sms_appointments: false,
    sms_messages: false,
    whatsapp_notifications: true
  });

  const [integrations, setIntegrations] = useState<IntegrationSettings>({
    whatsapp_token: '',
    whatsapp_phone: '',
    email_smtp_host: '',
    email_smtp_port: '',
    email_smtp_user: '',
    email_smtp_password: ''
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/settings');
      if (!response.ok) throw new Error('Failed to fetch settings');
      const data = await response.json();
      
      if (data.profile) setProfile(data.profile);
      if (data.notifications) setNotifications(data.notifications);
      if (data.integrations) setIntegrations(data.integrations);
    } catch (error) {
      toast({
        title: "Erro ao carregar configurações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const saveProfile = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/settings/profile', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profile)
      });

      if (!response.ok) throw new Error('Failed to save profile');

      toast({
        title: "Perfil atualizado",
        description: "Suas informações foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar perfil",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const saveNotifications = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/settings/notifications', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(notifications)
      });

      if (!response.ok) throw new Error('Failed to save notifications');

      toast({
        title: "Notificações atualizadas",
        description: "Suas preferências foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar notificações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const saveIntegrations = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/settings/integrations', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(integrations)
      });

      if (!response.ok) throw new Error('Failed to save integrations');

      toast({
        title: "Integrações atualizadas",
        description: "Suas configurações foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar integrações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Configurações</h1>
        <p className="text-muted-foreground">Gerencie suas preferências e integrações</p>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            Perfil
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center">
            <Bell className="mr-2 h-4 w-4" />
            Notificações
          </TabsTrigger>
          <TabsTrigger value="integrations" className="flex items-center">
            <Smartphone className="mr-2 h-4 w-4" />
            Integrações
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center">
            <Shield className="mr-2 h-4 w-4" />
            Segurança
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5 text-primary" />
                Informações Pessoais
              </CardTitle>
              <CardDescription>
                Atualize suas informações pessoais e profissionais
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome Completo</Label>
                  <Input
                    id="name"
                    value={profile.name}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    value={profile.phone}
                    onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="specialty">Especialidade</Label>
                  <Input
                    id="specialty"
                    value={profile.specialty}
                    onChange={(e) => setProfile({ ...profile, specialty: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="crm">CRM</Label>
                  <Input
                    id="crm"
                    value={profile.crm}
                    onChange={(e) => setProfile({ ...profile, crm: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clinic_name">Nome da Clínica</Label>
                  <Input
                    id="clinic_name"
                    value={profile.clinic_name}
                    onChange={(e) => setProfile({ ...profile, clinic_name: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="clinic_address">Endereço da Clínica</Label>
                <Textarea
                  id="clinic_address"
                  value={profile.clinic_address}
                  onChange={(e) => setProfile({ ...profile, clinic_address: e.target.value })}
                />
              </div>

              <Button onClick={saveProfile} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Perfil'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Smartphone className="mr-2 h-5 w-5 text-primary" />
                Integrações
              </CardTitle>
              <CardDescription>
                Configure integrações com WhatsApp e email
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">WhatsApp Business API</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="whatsapp_token">Token de Acesso</Label>
                    <Input
                      id="whatsapp_token"
                      type="password"
                      value={integrations.whatsapp_token}
                      onChange={(e) => setIntegrations({ ...integrations, whatsapp_token: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="whatsapp_phone">Número do WhatsApp</Label>
                    <Input
                      id="whatsapp_phone"
                      value={integrations.whatsapp_phone}
                      onChange={(e) => setIntegrations({ ...integrations, whatsapp_phone: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Configurações de Email</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_host">Servidor SMTP</Label>
                    <Input
                      id="email_smtp_host"
                      value={integrations.email_smtp_host}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_host: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_port">Porta SMTP</Label>
                    <Input
                      id="email_smtp_port"
                      value={integrations.email_smtp_port}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_port: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_user">Usuário SMTP</Label>
                    <Input
                      id="email_smtp_user"
                      value={integrations.email_smtp_user}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_user: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_password">Senha SMTP</Label>
                    <Input
                      id="email_smtp_password"
                      type="password"
                      value={integrations.email_smtp_password}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_password: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              <Button onClick={saveIntegrations} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Integrações'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5 text-primary" />
                Segurança
              </CardTitle>
              <CardDescription>
                Gerencie configurações de segurança da sua conta
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Alterar Senha</h4>
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="current_password">Senha Atual</Label>
                    <Input id="current_password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new_password">Nova Senha</Label>
                    <Input id="new_password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm_password">Confirmar Nova Senha</Label>
                    <Input id="confirm_password" type="password" />
                  </div>
                </div>
                <Button variant="outline">
                  Alterar Senha
                </Button>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Autenticação de Dois Fatores</h4>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm">Ativar 2FA</p>
                    <p className="text-sm text-muted-foreground">
                      Adicione uma camada extra de segurança à sua conta
                    </p>
                  </div>
                  <Switch />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Sessões Ativas</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="text-sm font-medium">Sessão atual</p>
                      <p className="text-sm text-muted-foreground">Windows • Chrome • São Paulo, BR</p>
                    </div>
                    <Button variant="outline" size="sm">
                      Encerrar
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsPage;

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="mr-2 h-5 w-5 text-primary" />
                Preferências de Notificação
              </CardTitle>
              <CardDescription>
                Configure como você deseja receber notificações
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Notificações por Email</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email_appointments">Consultas agendadas</Label>
                      <p className="text-sm text-muted-foreground">Receber emails sobre novas consultas</p>
                    </div>
                    <Switch
                      id="email_appointments"
                      checked={notifications.email_appointments}
                      onCheckedChange={(checked) => 
                        setNotifications({ ...notifications, email_appointments: checked })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email_messages">Novas mensagens</Label>
                      <p className="text-sm text-muted-foreground">Receber emails sobre mensagens de pacientes</p>
                    </div>
                    <Switch
                      id="email_messages"
                      checked={notifications.email_messages}
                      onCheckedChange={(checked) => 
                        setNotifications({ ...notifications, email_messages: checked })
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Notificações por WhatsApp</h4>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="whatsapp_notifications">Notificações gerais</Label>
                    <p className="text-sm text-muted-foreground">Receber notificações via WhatsApp</p>
                  </div>
                  <Switch
                    id="whatsapp_notifications"
                    checked={notifications.whatsapp_notifications}
                    onCheckedChange={(checked) => 
                      setNotifications({ ...notifications, whatsapp_notifications: checked })
                    }
                  />
                </div>
              </div>

              <Button onClick={saveNotifications} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Notificações'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
