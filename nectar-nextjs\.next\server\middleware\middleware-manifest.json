{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "YJe7OyuQCjzPw186m+1UYw51RjmWUlL+OPw7wyTvmcI=", "__NEXT_PREVIEW_MODE_ID": "7e42a5e2bf28f7d1267af3c4ea8e5fb2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "89af190052ddb3f54bbfb5556ea0d5f37c09da401094500853b389160550b7b0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fd74a1d00789b22f1e870b381f081204e11b5aad75d6c64d0a912e4efde86916"}}}, "instrumentation": null, "functions": {}}