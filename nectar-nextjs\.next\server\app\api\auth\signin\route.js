(()=>{var e={};e.id=957,e.ids=[957],e.modules={1001:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>c});var i=t(96559),n=t(48088),o=t(37719),u=t(2507),a=t(53171);async function c(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return(0,a.Wg)(void 0,"Email and password are required",400);let s=await (0,u.U)(),{data:i,error:n}=await s.auth.signInWithPassword({email:r,password:t});if(n)return(0,a.Wg)(void 0,n.message,401);return(0,a.Wg)({user:i.user,session:i.session})}catch(e){return(0,a.hS)(e)}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/signin/route",pathname:"/api/auth/signin",filename:"route",bundlePath:"app/api/auth/signin/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signin\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=p;function v(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>n});var s=t(9866),i=t(44999);async function n(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://zmwdnemlzndjavlriyrc.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set(r,t,s))}catch{}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},53171:(e,r,t)=>{"use strict";t.d(r,{Wg:()=>n,hS:()=>u,ru:()=>o});var s=t(32190),i=t(2507);function n(e,r,t=200){return s.NextResponse.json({data:e,error:r},{status:t})}async function o(e,r){try{let e=await (0,i.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return n(void 0,"Unauthorized",401);return await r(t.id,e)}catch(e){return console.error("API Error:",e),n(void 0,e instanceof Error?e.message:"Internal server error",500)}}function u(e){return(console.error("API Error:",e),e?.code==="PGRST116")?n(void 0,"Resource not found",404):e?.code==="23505"?n(void 0,"Resource already exists",409):n(void 0,e?.message||"Internal server error",500)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[524,391],()=>t(1001));module.exports=s})();