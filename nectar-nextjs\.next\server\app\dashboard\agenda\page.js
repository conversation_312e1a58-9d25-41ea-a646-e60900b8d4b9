(()=>{var e={};e.id=250,e.ids=[250],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10280:(e,t,r)=>{Promise.resolve().then(r.bind(r,88598))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>f,eb:()=>y,gC:()=>p,l6:()=>c,yv:()=>u});var a=r(60687),n=r(43210),o=r(72951),s=r(78272),i=r(3589),l=r(13964),d=r(4780);let c=o.bL;o.YJ;let u=o.WT,f=n.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(o.l9,{ref:n,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,a.jsx)(o.In,{asChild:!0,children:(0,a.jsx)(s.A,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=o.l9.displayName;let m=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.PP,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));m.displayName=o.PP.displayName;let h=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.wn,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(s.A,{className:"h-4 w-4"})}));h.displayName=o.wn.displayName;let p=n.forwardRef(({className:e,children:t,position:r="popper",...n},s)=>(0,a.jsx)(o.ZL,{children:(0,a.jsxs)(o.UC,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,a.jsx)(m,{}),(0,a.jsx)(o.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(h,{})]})}));p.displayName=o.UC.displayName,n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.JU,{ref:r,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=o.JU.displayName;let y=n.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(o.q7,{ref:n,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(o.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(o.p4,{children:t})]}));y.displayName=o.q7.displayName,n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.wv,{ref:r,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=o.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34334:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var a=r(65239),n=r(48088),o=r(88170),s=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["agenda",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83742)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\agenda\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\agenda\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/agenda/page",pathname:"/dashboard/agenda",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var a=r(60687),n=r(43210),o=r(4780);let s=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));s.displayName="Textarea"},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>s,aR:()=>i});var a=r(60687),n=r(43210),o=r(4780);let s=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));s.displayName="Card";let i=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},46728:(e,t,r)=>{Promise.resolve().then(r.bind(r,83742))},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var a=r(60687),n=r(43210),o=r(14163),s=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var i=r(24224),l=r(4780);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(s,{ref:r,className:(0,l.cn)(d(),e),...t}));c.displayName=s.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>f,Es:()=>h,L3:()=>p,c7:()=>m,lG:()=>l,rr:()=>y,zM:()=>d});var a=r(60687),n=r(43210),o=r(26134),s=r(11860),i=r(4780);let l=o.bL,d=o.l9,c=o.ZL;o.bm;let u=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.hJ,{ref:r,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=o.hJ.displayName;let f=n.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(o.UC,{ref:n,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,a.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(s.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=o.UC.displayName;let m=({className:e,...t})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let h=({className:e,...t})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="DialogFooter";let p=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.hE,{ref:r,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));p.displayName=o.hE.displayName;let y=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.VY,{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));y.displayName=o.VY.displayName},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83742:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\agenda\\page.tsx","default")},86401:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});var a=r(79481);async function n(e,t={}){let r=(0,a.U)(),{data:{session:o}}=await r.auth.getSession(),s={"Content-Type":"application/json",...t.headers};return o?.access_token&&(s.Authorization=`Bearer ${o.access_token}`),fetch(e,{...t,headers:s,credentials:"include"})}},88598:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tx});var a,n,o,s,i,l={};r.r(l),r.d(l,{Button:()=>ee,CaptionLabel:()=>et,Chevron:()=>er,Day:()=>ea,DayButton:()=>en,Dropdown:()=>eo,DropdownNav:()=>es,Footer:()=>ei,Month:()=>el,MonthCaption:()=>ed,MonthGrid:()=>ec,Months:()=>eu,MonthsDropdown:()=>eh,Nav:()=>ep,NextMonthButton:()=>ey,Option:()=>ev,PreviousMonthButton:()=>eg,Root:()=>ex,Select:()=>eb,Week:()=>ew,WeekNumber:()=>eD,WeekNumberHeader:()=>eM,Weekday:()=>eN,Weekdays:()=>ek,Weeks:()=>ej,YearsDropdown:()=>eC});var d={};r.r(d),r.d(d,{formatCaption:()=>e_,formatDay:()=>eO,formatMonthCaption:()=>eE,formatMonthDropdown:()=>eS,formatWeekNumber:()=>eW,formatWeekNumberHeader:()=>eT,formatWeekdayName:()=>eL,formatYearCaption:()=>eY,formatYearDropdown:()=>eA});var c={};r.r(c),r.d(c,{labelCaption:()=>eP,labelDay:()=>eq,labelDayButton:()=>eZ,labelGrid:()=>eF,labelGridcell:()=>eI,labelMonthDropdown:()=>eB,labelNav:()=>eR,labelNext:()=>eU,labelPrevious:()=>e$,labelWeekNumber:()=>eH,labelWeekNumberHeader:()=>eG,labelWeekday:()=>ez,labelYearDropdown:()=>eJ});var u=r(60687),f=r(43210),m=r(62688);let h=(0,m.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),p=(0,m.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);Symbol.for("constructDateFrom");let y={},v={};function g(e,t){try{let r=(y[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(r in v)return v[r];return b(r,r.split(":"))}catch{if(e in v)return v[e];let t=e?.match(x);if(t)return b(e,t.slice(1));return NaN}}let x=/([+-]\d\d):?(\d\d)?/;function b(e,t){let r=+t[0],a=+(t[1]||0);return v[e]=r>0?60*r+a:60*r-a}class w extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(g(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),D(this,NaN),k(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new w(...t,e):new w(Date.now(),e)}withTimeZone(e){return new w(+this,e)}getTimezoneOffset(){return-g(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),k(this),+this}[Symbol.for("constructDateFrom")](e){return new w(+new Date(e),this.timeZone)}}let N=/^(get|set)(?!UTC)/;function k(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function D(e){let t=g(e.timeZone,e),r=new Date(+e);r.setUTCHours(r.getUTCHours()-1);let a=-new Date(+e).getTimezoneOffset(),n=a- -new Date(+r).getTimezoneOffset(),o=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();n&&o&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+n);let s=a-t;s&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+s);let i=g(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-i-s;if(i!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=i-g(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!N.test(e))return;let t=e.replace(N,"$1UTC");w.prototype[t]&&(e.startsWith("get")?w.prototype[e]=function(){return this.internal[t]()}:(w.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),D(e),+this},w.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),k(this),+this}))});class M extends w{static tz(e,...t){return t.length?new M(...t,e):new M(Date.now(),e)}toISOString(){let[e,t,r]=this.tzComponents(),a=`${e}${t}:${r}`;return this.internal.toISOString().slice(0,-1)+a}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,r,a]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${r} ${t} ${a}`}toTimeString(){var e,t;let r=this.internal.toUTCString().split(" ")[4],[a,n,o]=this.tzComponents();return`${r} GMT${a}${n}${o} (${e=this.timeZone,t=this,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),r=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,r]}withTimeZone(e){return new M(+this,e)}[Symbol.for("constructDateFrom")](e){return new M(+new Date(e),this.timeZone)}}!function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"}(a||(a={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(n||(n={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(o||(o={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(s||(s={}));var j=r(64722),C=r(87981),_=r(23711);function E(e,t,r){let a=(0,_.a)(e,r?.in);return isNaN(t)?(0,C.w)(r?.in||e,NaN):(t&&a.setDate(a.getDate()+t),a)}function O(e,t,r){let a=(0,_.a)(e,r?.in);if(isNaN(t))return(0,C.w)(r?.in||e,NaN);if(!t)return a;let n=a.getDate(),o=(0,C.w)(r?.in||e,a.getTime());return(o.setMonth(a.getMonth()+t+1,0),n>=o.getDate())?o:(a.setFullYear(o.getFullYear(),o.getMonth(),n),a)}var S=r(48750),W=r(29789),T=r(78872);function L(e,t){let r=(0,T.q)(),a=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,n=(0,_.a)(e,t?.in),o=n.getDay();return n.setDate(n.getDate()+((o<a?-7:0)+6-(o-a))),n.setHours(23,59,59,999),n}var A=r(85650),Y=r(38832),F=r(46495),P=r(91522),I=r(30319),Z=r(64916),q=r(51877),R=r(27272);function B(e,t){let r=t.startOfMonth(e),a=r.getDay();return 1===a?r:0===a?t.addDays(r,-6):t.addDays(r,-1*(a-1))}class U{constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?M.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,r)=>this.overrides?.newDate?this.overrides.newDate(e,t,r):this.options.timeZone?new M(e,t,r,this.options.timeZone):new Date(e,t,r),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):E(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):O(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):E(e,7*t,void 0),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):O(e,12*t,void 0),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):(0,S.m)(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):function(e,t,r){let[a,n]=(0,W.x)(void 0,e,t);return 12*(a.getFullYear()-n.getFullYear())+(a.getMonth()-n.getMonth())}(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){let{start:r,end:a}=function(e,t){let[r,a]=(0,W.x)(e,t.start,t.end);return{start:r,end:a}}(void 0,e),n=+r>+a,o=n?+r:+a,s=n?a:r;s.setHours(0,0,0,0),s.setDate(1);let i=(void 0)??1;if(!i)return[];i<0&&(i=-i,n=!n);let l=[];for(;+s<=o;)l.push((0,C.w)(r,s)),s.setMonth(s.getMonth()+i);return n?l.reverse():l}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e):function(e,t){let r=B(e,t),a=function(e,t){let r=t.startOfMonth(e),a=r.getDay()>0?r.getDay():7,n=t.addDays(e,-a+1),o=t.addDays(n,34);return t.getMonth(e)===t.getMonth(o)?5:4}(e,t);return t.addDays(r,7*a-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):L(e,{...void 0,weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):function(e,t){let r=(0,_.a)(e,void 0),a=r.getMonth();return r.setFullYear(r.getFullYear(),a+1,0),r.setHours(23,59,59,999),r}(e),this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,t):L(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e,t){let r=(0,_.a)(e,void 0),a=r.getFullYear();return r.setFullYear(a+1,0,0),r.setHours(23,59,59,999),r}(e),this.format=(e,t,r)=>{let a=this.overrides?.format?this.overrides.format(e,t,this.options):(0,A.GP)(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(a):a},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):(0,Y.s)(e),this.getMonth=(e,t)=>{var r;return this.overrides?.getMonth?this.overrides.getMonth(e,this.options):(r=this.options,(0,_.a)(e,r?.in).getMonth())},this.getYear=(e,t)=>{var r;return this.overrides?.getYear?this.overrides.getYear(e,this.options):(r=this.options,(0,_.a)(e,r?.in).getFullYear())},this.getWeek=(e,t)=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):(0,F.N)(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):+(0,_.a)(e)>+(0,_.a)(t),this.isBefore=(e,t)=>this.overrides?.isBefore?this.overrides.isBefore(e,t):+(0,_.a)(e)<+(0,_.a)(t),this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):(0,P.$)(e),this.isSameDay=(e,t)=>this.overrides?.isSameDay?this.overrides.isSameDay(e,t):function(e,t,r){let[a,n]=(0,W.x)(void 0,e,t);return+(0,I.o)(a)==+(0,I.o)(n)}(e,t),this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):function(e,t,r){let[a,n]=(0,W.x)(void 0,e,t);return a.getFullYear()===n.getFullYear()&&a.getMonth()===n.getMonth()}(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t,r){let[a,n]=(0,W.x)(void 0,e,t);return a.getFullYear()===n.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e,t){let r,a;return e.forEach(e=>{a||"object"!=typeof e||(a=C.w.bind(null,e));let t=(0,_.a)(e,a);(!r||r<t||isNaN(+t))&&(r=t)}),(0,C.w)(a,r||NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e,t){let r,a;return e.forEach(e=>{a||"object"!=typeof e||(a=C.w.bind(null,e));let t=(0,_.a)(e,a);(!r||r>t||isNaN(+t))&&(r=t)}),(0,C.w)(a,r||NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):function(e,t,r){let a=(0,_.a)(e,void 0),n=a.getFullYear(),o=a.getDate(),s=(0,C.w)(e,0);s.setFullYear(n,t,15),s.setHours(0,0,0,0);let i=function(e,t){let r=(0,_.a)(e,void 0),a=r.getFullYear(),n=r.getMonth(),o=(0,C.w)(r,0);return o.setFullYear(a,n+1,0),o.setHours(0,0,0,0),o.getDate()}(s);return a.setMonth(t,Math.min(o,i)),a}(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t,r){let a=(0,_.a)(e,void 0);return isNaN(+a)?(0,C.w)(e,NaN):(a.setFullYear(t),a)}(e,t),this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):B(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):(0,I.o)(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):(0,Z.b)(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):function(e,t){let r=(0,_.a)(e,void 0);return r.setDate(1),r.setHours(0,0,0,0),r}(e),this.startOfWeek=(e,t)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):(0,q.k)(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):(0,R.D)(e),this.options={locale:j.c,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),r={};for(let e=0;e<10;e++)r[e.toString()]=t.format(e);return r}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let $=new U;function z(e,t,r=!1,a=$){let{from:n,to:o}=e,{differenceInCalendarDays:s,isSameDay:i}=a;return n&&o?(0>s(o,n)&&([n,o]=[o,n]),s(t,n)>=+!!r&&s(o,t)>=+!!r):!r&&o?i(o,t):!r&&!!n&&i(n,t)}function H(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function G(e){return!!(e&&"object"==typeof e&&"from"in e)}function J(e){return!!(e&&"object"==typeof e&&"after"in e)}function K(e){return!!(e&&"object"==typeof e&&"before"in e)}function V(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function X(e,t){return Array.isArray(e)&&e.every(t.isDate)}function Q(e,t,r=$){let a=Array.isArray(t)?t:[t],{isSameDay:n,differenceInCalendarDays:o,isAfter:s}=r;return a.some(t=>{if("boolean"==typeof t)return t;if(r.isDate(t))return n(e,t);if(X(t,r))return t.includes(e);if(G(t))return z(t,e,!1,r);if(V(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(H(t)){let r=o(t.before,e),a=o(t.after,e),n=r>0,i=a<0;return s(t.before,t.after)?i&&n:n||i}return J(t)?o(e,t.after)>0:K(t)?o(t.before,e)>0:"function"==typeof t&&t(e)})}function ee(e){return f.createElement("button",{...e})}function et(e){return f.createElement("span",{...e})}function er(e){let{size:t=24,orientation:r="left",className:a}=e;return f.createElement("svg",{className:a,width:t,height:t,viewBox:"0 0 24 24"},"up"===r&&f.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===r&&f.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===r&&f.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===r&&f.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function ea(e){let{day:t,modifiers:r,...a}=e;return f.createElement("td",{...a})}function en(e){let{day:t,modifiers:r,...a}=e,n=f.useRef(null);return f.useEffect(()=>{r.focused&&n.current?.focus()},[r.focused]),f.createElement("button",{ref:n,...a})}function eo(e){let{options:t,className:r,components:n,classNames:o,...s}=e,i=[o[a.Dropdown],r].join(" "),l=t?.find(({value:e})=>e===s.value);return f.createElement("span",{"data-disabled":s.disabled,className:o[a.DropdownRoot]},f.createElement(n.Select,{className:i,...s},t?.map(({value:e,label:t,disabled:r})=>f.createElement(n.Option,{key:e,value:e,disabled:r},t))),f.createElement("span",{className:o[a.CaptionLabel],"aria-hidden":!0},l?.label,f.createElement(n.Chevron,{orientation:"down",size:18,className:o[a.Chevron]})))}function es(e){return f.createElement("div",{...e})}function ei(e){return f.createElement("div",{...e})}function el(e){let{calendarMonth:t,displayIndex:r,...a}=e;return f.createElement("div",{...a},e.children)}function ed(e){let{calendarMonth:t,displayIndex:r,...a}=e;return f.createElement("div",{...a})}function ec(e){return f.createElement("table",{...e})}function eu(e){return f.createElement("div",{...e})}let ef=(0,f.createContext)(void 0);function em(){let e=(0,f.useContext)(ef);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}function eh(e){let{components:t}=em();return f.createElement(t.Dropdown,{...e})}function ep(e){let{onPreviousClick:t,onNextClick:r,previousMonth:n,nextMonth:o,...s}=e,{components:i,classNames:l,labels:{labelPrevious:d,labelNext:c}}=em(),u=(0,f.useCallback)(e=>{o&&r?.(e)},[o,r]),m=(0,f.useCallback)(e=>{n&&t?.(e)},[n,t]);return f.createElement("nav",{...s},f.createElement(i.PreviousMonthButton,{type:"button",className:l[a.PreviousMonthButton],tabIndex:n?void 0:-1,"aria-disabled":!n||void 0,"aria-label":d(n),onClick:m},f.createElement(i.Chevron,{disabled:!n||void 0,className:l[a.Chevron],orientation:"left"})),f.createElement(i.NextMonthButton,{type:"button",className:l[a.NextMonthButton],tabIndex:o?void 0:-1,"aria-disabled":!o||void 0,"aria-label":c(o),onClick:u},f.createElement(i.Chevron,{disabled:!o||void 0,orientation:"right",className:l[a.Chevron]})))}function ey(e){let{components:t}=em();return f.createElement(t.Button,{...e})}function ev(e){return f.createElement("option",{...e})}function eg(e){let{components:t}=em();return f.createElement(t.Button,{...e})}function ex(e){let{rootRef:t,...r}=e;return f.createElement("div",{...r,ref:t})}function eb(e){return f.createElement("select",{...e})}function ew(e){let{week:t,...r}=e;return f.createElement("tr",{...r})}function eN(e){return f.createElement("th",{...e})}function ek(e){return f.createElement("thead",{"aria-hidden":!0},f.createElement("tr",{...e}))}function eD(e){let{week:t,...r}=e;return f.createElement("th",{...r})}function eM(e){return f.createElement("th",{...e})}function ej(e){return f.createElement("tbody",{...e})}function eC(e){let{components:t}=em();return f.createElement(t.Dropdown,{...e})}function e_(e,t,r){return(r??new U(t)).format(e,"LLLL y")}let eE=e_;function eO(e,t,r){return(r??new U(t)).format(e,"d")}function eS(e,t=$){return t.format(e,"LLLL")}function eW(e,t=$){return e<10?t.formatNumber(`0${e.toLocaleString()}`):t.formatNumber(`${e.toLocaleString()}`)}function eT(){return""}function eL(e,t,r){return(r??new U(t)).format(e,"cccccc")}function eA(e,t=$){return t.format(e,"yyyy")}let eY=eA;function eF(e,t,r){return(r??new U(t)).format(e,"LLLL y")}let eP=eF;function eI(e,t,r,a){let n=(a??new U(r)).format(e,"PPPP");return t?.today&&(n=`Today, ${n}`),n}function eZ(e,t,r,a){let n=(a??new U(r)).format(e,"PPPP");return t.today&&(n=`Today, ${n}`),t.selected&&(n=`${n}, selected`),n}let eq=eZ;function eR(){return""}function eB(e){return"Choose the Month"}function eU(e){return"Go to the Next Month"}function e$(e){return"Go to the Previous Month"}function ez(e,t,r){return(r??new U(t)).format(e,"cccc")}function eH(e,t){return`Week ${e}`}function eG(e){return"Week Number"}function eJ(e){return"Choose the Year"}let eK=e=>e instanceof HTMLElement?e:null,eV=e=>[...e.querySelectorAll("[data-animated-month]")??[]],eX=e=>eK(e.querySelector("[data-animated-month]")),eQ=e=>eK(e.querySelector("[data-animated-caption]")),e0=e=>eK(e.querySelector("[data-animated-weeks]")),e1=e=>eK(e.querySelector("[data-animated-nav]")),e2=e=>eK(e.querySelector("[data-animated-weekdays]"));function e4(e,t,r,a){let{month:n,defaultMonth:o,today:s=a.today(),numberOfMonths:i=1}=e,l=n||o||s,{differenceInCalendarMonths:d,addMonths:c,startOfMonth:u}=a;return r&&d(r,l)<i-1&&(l=c(r,-1*(i-1))),t&&0>d(l,t)&&(l=t),u(l)}class e8{constructor(e,t,r=$){this.date=e,this.displayMonth=t,this.outside=!!(t&&!r.isSameMonth(e,t)),this.dateLib=r}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class e3{constructor(e,t){this.days=t,this.weekNumber=e}}class e5{constructor(e,t){this.date=e,this.weeks=t}}function e6(e,t){let[r,a]=(0,f.useState)(e);return[void 0===t?r:t,a]}function e9(e){return!e[n.disabled]&&!e[n.hidden]&&!e[n.outside]}function e7(e,t,r=$){return z(e,t.from,!1,r)||z(e,t.to,!1,r)||z(t,e.from,!1,r)||z(t,e.to,!1,r)}function te(e){let t=e;t.timeZone&&((t={...e}).today&&(t.today=new M(t.today,t.timeZone)),t.month&&(t.month=new M(t.month,t.timeZone)),t.defaultMonth&&(t.defaultMonth=new M(t.defaultMonth,t.timeZone)),t.startMonth&&(t.startMonth=new M(t.startMonth,t.timeZone)),t.endMonth&&(t.endMonth=new M(t.endMonth,t.timeZone)),"single"===t.mode&&t.selected?t.selected=new M(t.selected,t.timeZone):"multiple"===t.mode&&t.selected?t.selected=t.selected?.map(e=>new M(e,t.timeZone)):"range"===t.mode&&t.selected&&(t.selected={from:t.selected.from?new M(t.selected.from,t.timeZone):void 0,to:t.selected.to?new M(t.selected.to,t.timeZone):void 0}));let{components:r,formatters:u,labels:m,dateLib:h,locale:p,classNames:y}=(0,f.useMemo)(()=>{var e,r;let i={...j.c,...t.locale};return{dateLib:new U({locale:i,weekStartsOn:t.broadcastCalendar?1:t.weekStartsOn,firstWeekContainsDate:t.firstWeekContainsDate,useAdditionalWeekYearTokens:t.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:t.useAdditionalDayOfYearTokens,timeZone:t.timeZone,numerals:t.numerals},t.dateLib),components:(e=t.components,{...l,...e}),formatters:(r=t.formatters,r?.formatMonthCaption&&!r.formatCaption&&(r.formatCaption=r.formatMonthCaption),r?.formatYearCaption&&!r.formatYearDropdown&&(r.formatYearDropdown=r.formatYearCaption),{...d,...r}),labels:{...c,...t.labels},locale:i,classNames:{...function(){let e={};for(let t in a)e[a[t]]=`rdp-${a[t]}`;for(let t in n)e[n[t]]=`rdp-${n[t]}`;for(let t in o)e[o[t]]=`rdp-${o[t]}`;for(let t in s)e[s[t]]=`rdp-${s[t]}`;return e}(),...t.classNames}}},[t.locale,t.broadcastCalendar,t.weekStartsOn,t.firstWeekContainsDate,t.useAdditionalWeekYearTokens,t.useAdditionalDayOfYearTokens,t.timeZone,t.numerals,t.dateLib,t.components,t.formatters,t.labels,t.classNames]),{captionLayout:v,mode:g,navLayout:x,numberOfMonths:b=1,onDayBlur:w,onDayClick:N,onDayFocus:k,onDayKeyDown:D,onDayMouseEnter:C,onDayMouseLeave:_,onNextClick:E,onPrevClick:O,showWeekNumber:S,styles:W}=t,{formatCaption:T,formatDay:L,formatMonthDropdown:A,formatWeekNumber:Y,formatWeekNumberHeader:F,formatWeekdayName:P,formatYearDropdown:I}=u,Z=function(e,t){let[r,a]=function(e,t){let{startMonth:r,endMonth:a}=e,{startOfYear:n,startOfDay:o,startOfMonth:s,endOfMonth:i,addYears:l,endOfYear:d,newDate:c,today:u}=t,{fromYear:f,toYear:m,fromMonth:h,toMonth:p}=e;!r&&h&&(r=h),!r&&f&&(r=t.newDate(f,0,1)),!a&&p&&(a=p),!a&&m&&(a=c(m,11,31));let y="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return r?r=s(r):f?r=c(f,0,1):!r&&y&&(r=n(l(e.today??u(),-100))),a?a=i(a):m?a=c(m,11,31):!a&&y&&(a=d(e.today??u())),[r?o(r):r,a?o(a):a]}(e,t),{startOfMonth:n,endOfMonth:o}=t,s=e4(e,r,a,t),[i,l]=e6(s,e.month?s:void 0);(0,f.useEffect)(()=>{l(e4(e,r,a,t))},[e.timeZone]);let d=function(e,t,r,a){let{numberOfMonths:n=1}=r,o=[];for(let r=0;r<n;r++){let n=a.addMonths(e,r);if(t&&n>t)break;o.push(n)}return o}(i,a,e,t),c=function(e,t,r,a){let n=e[0],o=e[e.length-1],{ISOWeek:s,fixedWeeks:i,broadcastCalendar:l}=r??{},{addDays:d,differenceInCalendarDays:c,differenceInCalendarMonths:u,endOfBroadcastWeek:f,endOfISOWeek:m,endOfMonth:h,endOfWeek:p,isAfter:y,startOfBroadcastWeek:v,startOfISOWeek:g,startOfWeek:x}=a,b=l?v(n,a):s?g(n):x(n),w=c(l?f(o):s?m(h(o)):p(h(o)),b),N=u(o,n)+1,k=[];for(let e=0;e<=w;e++){let r=d(b,e);if(t&&y(r,t))break;k.push(r)}let D=(l?35:42)*N;if(i&&k.length<D){let e=D-k.length;for(let t=0;t<e;t++){let e=d(k[k.length-1],1);k.push(e)}}return k}(d,e.endMonth?o(e.endMonth):void 0,e,t),u=function(e,t,r,a){let{addDays:n,endOfBroadcastWeek:o,endOfISOWeek:s,endOfMonth:i,endOfWeek:l,getISOWeek:d,getWeek:c,startOfBroadcastWeek:u,startOfISOWeek:f,startOfWeek:m}=a,h=e.reduce((e,h)=>{let p=r.broadcastCalendar?u(h,a):r.ISOWeek?f(h):m(h),y=r.broadcastCalendar?o(h):r.ISOWeek?s(i(h)):l(i(h)),v=t.filter(e=>e>=p&&e<=y),g=r.broadcastCalendar?35:42;if(r.fixedWeeks&&v.length<g){let e=t.filter(e=>{let t=g-v.length;return e>y&&e<=n(y,t)});v.push(...e)}let x=v.reduce((e,t)=>{let n=r.ISOWeek?d(t):c(t),o=e.find(e=>e.weekNumber===n),s=new e8(t,h,a);return o?o.days.push(s):e.push(new e3(n,[s])),e},[]),b=new e5(h,x);return e.push(b),e},[]);return r.reverseMonths?h.reverse():h}(d,c,e,t),m=u.reduce((e,t)=>[...e,...t.weeks],[]),h=function(e){let t=[];return e.reduce((e,r)=>[...e,...r.weeks.reduce((e,t)=>[...e,...t.days],t)],t)}(u),p=function(e,t,r,a){if(r.disableNavigation)return;let{pagedNavigation:n,numberOfMonths:o}=r,{startOfMonth:s,addMonths:i,differenceInCalendarMonths:l}=a,d=s(e);if(!t||!(0>=l(d,t)))return i(d,-(n?o??1:1))}(i,r,e,t),y=function(e,t,r,a){if(r.disableNavigation)return;let{pagedNavigation:n,numberOfMonths:o=1}=r,{startOfMonth:s,addMonths:i,differenceInCalendarMonths:l}=a,d=s(e);if(!t||!(l(t,e)<o))return i(d,n?o:1)}(i,a,e,t),{disableNavigation:v,onMonthChange:g}=e,x=e=>m.some(t=>t.days.some(t=>t.isEqualTo(e))),b=e=>{if(v)return;let t=n(e);r&&t<n(r)&&(t=n(r)),a&&t>n(a)&&(t=n(a)),l(t),g?.(t)};return{months:u,weeks:m,days:h,navStart:r,navEnd:a,previousMonth:p,nextMonth:y,goToMonth:b,goToDay:e=>{x(e)||b(e.date)}}}(t,h),{days:q,months:R,navStart:B,navEnd:ee,previousMonth:et,nextMonth:er,goToMonth:ea}=Z,en=function(e,t,r,a,o){let{disabled:s,hidden:i,modifiers:l,showOutsideDays:d,broadcastCalendar:c,today:u}=t,{isSameDay:f,isSameMonth:m,startOfMonth:h,isBefore:p,endOfMonth:y,isAfter:v}=o,g=r&&h(r),x=a&&y(a),b={[n.focused]:[],[n.outside]:[],[n.disabled]:[],[n.hidden]:[],[n.today]:[]},w={};for(let t of e){let{date:e,displayMonth:r}=t,a=!!(r&&!m(e,r)),n=!!(g&&p(e,g)),h=!!(x&&v(e,x)),y=!!(s&&Q(e,s,o)),N=!!(i&&Q(e,i,o))||n||h||!c&&!d&&a||c&&!1===d&&a,k=f(e,u??o.today());a&&b.outside.push(t),y&&b.disabled.push(t),N&&b.hidden.push(t),k&&b.today.push(t),l&&Object.keys(l).forEach(r=>{let a=l?.[r];a&&Q(e,a,o)&&(w[r]?w[r].push(t):w[r]=[t])})}return e=>{let t={[n.focused]:!1,[n.disabled]:!1,[n.hidden]:!1,[n.outside]:!1,[n.today]:!1},r={};for(let r in b){let a=b[r];t[r]=a.some(t=>t===e)}for(let t in w)r[t]=w[t].some(t=>t===e);return{...t,...r}}}(q,t,B,ee,h),{isSelected:eo,select:es,selected:ei}=function(e,t){let r=function(e,t){let{selected:r,required:a,onSelect:n}=e,[o,s]=e6(r,n?r:void 0),i=n?r:o,{isSameDay:l}=t;return{selected:i,select:(e,t,r)=>{let o=e;return!a&&i&&i&&l(e,i)&&(o=void 0),n||s(o),n?.(o,e,t,r),o},isSelected:e=>!!i&&l(i,e)}}(e,t),a=function(e,t){let{selected:r,required:a,onSelect:n}=e,[o,s]=e6(r,n?r:void 0),i=n?r:o,{isSameDay:l}=t,d=e=>i?.some(t=>l(t,e))??!1,{min:c,max:u}=e;return{selected:i,select:(e,t,r)=>{let o=[...i??[]];if(d(e)){if(i?.length===c||a&&i?.length===1)return;o=i?.filter(t=>!l(t,e))}else o=i?.length===u?[e]:[...o,e];return n||s(o),n?.(o,e,t,r),o},isSelected:d}}(e,t),n=function(e,t){let{disabled:r,excludeDisabled:a,selected:n,required:o,onSelect:s}=e,[i,l]=e6(n,s?n:void 0),d=s?n:i;return{selected:d,select:(n,i,c)=>{let{min:u,max:f}=e,m=n?function(e,t,r=0,a=0,n=!1,o=$){let s,{from:i,to:l}=t||{},{isSameDay:d,isAfter:c,isBefore:u}=o;if(i||l){if(i&&!l)s=d(i,e)?n?{from:i,to:void 0}:void 0:u(e,i)?{from:e,to:i}:{from:i,to:e};else if(i&&l)if(d(i,e)&&d(l,e))s=n?{from:i,to:l}:void 0;else if(d(i,e))s={from:i,to:r>0?void 0:e};else if(d(l,e))s={from:e,to:r>0?void 0:e};else if(u(e,i))s={from:e,to:l};else if(c(e,i))s={from:i,to:e};else if(c(e,l))s={from:i,to:e};else throw Error("Invalid range")}else s={from:e,to:r>0?void 0:e};if(s?.from&&s?.to){let t=o.differenceInCalendarDays(s.to,s.from);a>0&&t>a?s={from:e,to:void 0}:r>1&&t<r&&(s={from:e,to:void 0})}return s}(n,d,u,f,o,t):void 0;return a&&r&&m?.from&&m.to&&function(e,t,r=$){let a=Array.isArray(t)?t:[t];if(a.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:r.isDate(t)?z(e,t,!1,r):X(t,r)?t.some(t=>z(e,t,!1,r)):G(t)?!!t.from&&!!t.to&&e7(e,{from:t.from,to:t.to},r):V(t)?function(e,t,r=$){let a=Array.isArray(t)?t:[t],n=e.from,o=Math.min(r.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=o;e++){if(a.includes(n.getDay()))return!0;n=r.addDays(n,1)}return!1}(e,t.dayOfWeek,r):H(t)?r.isAfter(t.before,t.after)?e7(e,{from:r.addDays(t.after,1),to:r.addDays(t.before,-1)},r):Q(e.from,t,r)||Q(e.to,t,r):!!(J(t)||K(t))&&(Q(e.from,t,r)||Q(e.to,t,r))))return!0;let n=a.filter(e=>"function"==typeof e);if(n.length){let t=e.from,a=r.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=a;e++){if(n.some(e=>e(t)))return!0;t=r.addDays(t,1)}}return!1}({from:m.from,to:m.to},r,t)&&(m.from=n,m.to=void 0),s||l(m),s?.(m,n,i,c),m},isSelected:e=>d&&z(d,e,!1,t)}}(e,t);switch(e.mode){case"single":return r;case"multiple":return a;case"range":return n;default:return}}(t,h)??{},{blur:el,focused:ed,isFocusTarget:ec,moveFocus:eu,setFocused:em}=function(e,t,r,a,o){let{autoFocus:s}=e,[l,d]=(0,f.useState)(),c=function(e,t,r,a){let o,s=-1;for(let l of e){let e=t(l);e9(e)&&(e[n.focused]&&s<i.FocusedModifier?(o=l,s=i.FocusedModifier):a?.isEqualTo(l)&&s<i.LastFocused?(o=l,s=i.LastFocused):r(l.date)&&s<i.Selected?(o=l,s=i.Selected):e[n.today]&&s<i.Today&&(o=l,s=i.Today))}return o||(o=e.find(e=>e9(t(e)))),o}(t.days,r,a||(()=>!1),l),[u,m]=(0,f.useState)(s?c:void 0);return{isFocusTarget:e=>!!c?.isEqualTo(e),setFocused:m,focused:u,blur:()=>{d(u),m(void 0)},moveFocus:(r,a)=>{if(!u)return;let n=function e(t,r,a,n,o,s,i,l=0){if(l>365)return;let d=function(e,t,r,a,n,o,s){let{ISOWeek:i,broadcastCalendar:l}=o,{addDays:d,addMonths:c,addWeeks:u,addYears:f,endOfBroadcastWeek:m,endOfISOWeek:h,endOfWeek:p,max:y,min:v,startOfBroadcastWeek:g,startOfISOWeek:x,startOfWeek:b}=s,w=({day:d,week:u,month:c,year:f,startOfWeek:e=>l?g(e,s):i?x(e):b(e),endOfWeek:e=>l?m(e):i?h(e):p(e)})[e](r,"after"===t?1:-1);return"before"===t&&a?w=y([a,w]):"after"===t&&n&&(w=v([n,w])),w}(t,r,a.date,n,o,s,i),c=!!(s.disabled&&Q(d,s.disabled,i)),u=!!(s.hidden&&Q(d,s.hidden,i)),f=new e8(d,d,i);return c||u?e(t,r,f,n,o,s,i,l+1):f}(r,a,u,t.navStart,t.navEnd,e,o);n&&(t.goToDay(n),m(n))}}}(t,Z,en,eo??(()=>!1),h),{labelDayButton:eh,labelGridcell:ep,labelGrid:ey,labelMonthDropdown:ev,labelNav:eg,labelPrevious:ex,labelNext:eb,labelWeekday:ew,labelWeekNumber:eN,labelWeekNumberHeader:ek,labelYearDropdown:eD}=m,eM=(0,f.useMemo)(()=>(function(e,t,r){let a=e.today(),n=t?e.startOfISOWeek(a):e.startOfWeek(a),o=[];for(let t=0;t<7;t++){let r=e.addDays(n,t);o.push(r)}return o})(h,t.ISOWeek),[h,t.ISOWeek]),ej=void 0!==g||void 0!==N,eC=(0,f.useCallback)(()=>{et&&(ea(et),O?.(et))},[et,ea,O]),e_=(0,f.useCallback)(()=>{er&&(ea(er),E?.(er))},[ea,er,E]),eE=(0,f.useCallback)((e,t)=>r=>{r.preventDefault(),r.stopPropagation(),em(e),es?.(e.date,t,r),N?.(e.date,t,r)},[es,N,em]),eO=(0,f.useCallback)((e,t)=>r=>{em(e),k?.(e.date,t,r)},[k,em]),eS=(0,f.useCallback)((e,t)=>r=>{el(),w?.(e.date,t,r)},[el,w]),eW=(0,f.useCallback)((e,r)=>a=>{let n={ArrowLeft:[a.shiftKey?"month":"day","rtl"===t.dir?"after":"before"],ArrowRight:[a.shiftKey?"month":"day","rtl"===t.dir?"before":"after"],ArrowDown:[a.shiftKey?"year":"week","after"],ArrowUp:[a.shiftKey?"year":"week","before"],PageUp:[a.shiftKey?"year":"month","before"],PageDown:[a.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(n[a.key]){a.preventDefault(),a.stopPropagation();let[e,t]=n[a.key];eu(e,t)}D?.(e.date,r,a)},[eu,D,t.dir]),eT=(0,f.useCallback)((e,t)=>r=>{C?.(e.date,t,r)},[C]),eL=(0,f.useCallback)((e,t)=>r=>{_?.(e.date,t,r)},[_]),eA=(0,f.useCallback)(e=>t=>{let r=Number(t.target.value);ea(h.setMonth(h.startOfMonth(e),r))},[h,ea]),eY=(0,f.useCallback)(e=>t=>{let r=Number(t.target.value);ea(h.setYear(h.startOfMonth(e),r))},[h,ea]),{className:eF,style:eP}=(0,f.useMemo)(()=>({className:[y[a.Root],t.className].filter(Boolean).join(" "),style:{...W?.[a.Root],...t.style}}),[y,t.className,t.style,W]),eI=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0,"data-nav-layout":e.navLayout||void 0};return Object.entries(e).forEach(([e,r])=>{e.startsWith("data-")&&(t[e]=r)}),t}(t),eZ=(0,f.useRef)(null);!function(e,t,{classNames:r,months:a,focused:n,dateLib:o}){let i=(0,f.useRef)(null),l=(0,f.useRef)(a),d=(0,f.useRef)(!1);(0,f.useLayoutEffect)(()=>{let c=l.current;if(l.current=a,!t||!e.current||!(e.current instanceof HTMLElement)||0===a.length||0===c.length||a.length!==c.length)return;let u=o.isSameMonth(a[0].date,c[0].date),f=o.isAfter(a[0].date,c[0].date),m=f?r[s.caption_after_enter]:r[s.caption_before_enter],h=f?r[s.weeks_after_enter]:r[s.weeks_before_enter],p=i.current,y=e.current.cloneNode(!0);if(y instanceof HTMLElement?(eV(y).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=eX(e);t&&e.contains(t)&&e.removeChild(t);let r=eQ(e);r&&r.classList.remove(m);let a=e0(e);a&&a.classList.remove(h)}),i.current=y):i.current=null,d.current||u||n)return;let v=p instanceof HTMLElement?eV(p):[],g=eV(e.current);if(g&&g.every(e=>e instanceof HTMLElement)&&v&&v.every(e=>e instanceof HTMLElement)){d.current=!0;let t=[];e.current.style.isolation="isolate";let a=e1(e.current);a&&(a.style.zIndex="1"),g.forEach((n,o)=>{let i=v[o];if(!i)return;n.style.position="relative",n.style.overflow="hidden";let l=eQ(n);l&&l.classList.add(m);let c=e0(n);c&&c.classList.add(h);let u=()=>{d.current=!1,e.current&&(e.current.style.isolation=""),a&&(a.style.zIndex=""),l&&l.classList.remove(m),c&&c.classList.remove(h),n.style.position="",n.style.overflow="",n.contains(i)&&n.removeChild(i)};t.push(u),i.style.pointerEvents="none",i.style.position="absolute",i.style.overflow="hidden",i.setAttribute("aria-hidden","true");let p=e2(i);p&&(p.style.opacity="0");let y=eQ(i);y&&(y.classList.add(f?r[s.caption_before_exit]:r[s.caption_after_exit]),y.addEventListener("animationend",u));let g=e0(i);g&&g.classList.add(f?r[s.weeks_before_exit]:r[s.weeks_after_exit]),n.insertBefore(i,n.firstChild)})}})}(eZ,!!t.animate,{classNames:y,months:R,focused:ed,dateLib:h});let eq={dayPickerProps:t,selected:ei,select:es,isSelected:eo,months:R,nextMonth:er,previousMonth:et,goToMonth:ea,getModifiers:en,components:r,classNames:y,styles:W,labels:m,formatters:u};return f.createElement(ef.Provider,{value:eq},f.createElement(r.Root,{rootRef:t.animate?eZ:void 0,className:eF,style:eP,dir:t.dir,id:t.id,lang:t.lang,nonce:t.nonce,title:t.title,role:t.role,"aria-label":t["aria-label"],...eI},f.createElement(r.Months,{className:y[a.Months],style:W?.[a.Months]},!t.hideNavigation&&!x&&f.createElement(r.Nav,{"data-animated-nav":t.animate?"true":void 0,className:y[a.Nav],style:W?.[a.Nav],"aria-label":eg(),onPreviousClick:eC,onNextClick:e_,previousMonth:et,nextMonth:er}),R.map((e,s)=>{let i=function(e,t,r,a,n){let{startOfMonth:o,startOfYear:s,endOfYear:i,eachMonthOfInterval:l,getMonth:d}=n;return l({start:s(e),end:i(e)}).map(e=>{let s=a.formatMonthDropdown(e,n);return{value:d(e),label:s,disabled:t&&e<o(t)||r&&e>o(r)||!1}})}(e.date,B,ee,u,h),l=function(e,t,r,a){if(!e||!t)return;let{startOfYear:n,endOfYear:o,addYears:s,getYear:i,isBefore:l,isSameYear:d}=a,c=n(e),u=o(t),f=[],m=c;for(;l(m,u)||d(m,u);)f.push(m),m=s(m,1);return f.map(e=>{let t=r.formatYearDropdown(e,a);return{value:i(e),label:t,disabled:!1}})}(B,ee,u,h);return f.createElement(r.Month,{"data-animated-month":t.animate?"true":void 0,className:y[a.Month],style:W?.[a.Month],key:s,displayIndex:s,calendarMonth:e},"around"===x&&!t.hideNavigation&&0===s&&f.createElement(r.PreviousMonthButton,{type:"button",className:y[a.PreviousMonthButton],tabIndex:et?void 0:-1,"aria-disabled":!et||void 0,"aria-label":ex(et),onClick:eC,"data-animated-button":t.animate?"true":void 0},f.createElement(r.Chevron,{disabled:!et||void 0,className:y[a.Chevron],orientation:"rtl"===t.dir?"right":"left"})),f.createElement(r.MonthCaption,{"data-animated-caption":t.animate?"true":void 0,className:y[a.MonthCaption],style:W?.[a.MonthCaption],calendarMonth:e,displayIndex:s},v?.startsWith("dropdown")?f.createElement(r.DropdownNav,{className:y[a.Dropdowns],style:W?.[a.Dropdowns]},"dropdown"===v||"dropdown-months"===v?f.createElement(r.MonthsDropdown,{className:y[a.MonthsDropdown],"aria-label":ev(),classNames:y,components:r,disabled:!!t.disableNavigation,onChange:eA(e.date),options:i,style:W?.[a.Dropdown],value:h.getMonth(e.date)}):f.createElement("span",null,A(e.date,h)),"dropdown"===v||"dropdown-years"===v?f.createElement(r.YearsDropdown,{className:y[a.YearsDropdown],"aria-label":eD(h.options),classNames:y,components:r,disabled:!!t.disableNavigation,onChange:eY(e.date),options:l,style:W?.[a.Dropdown],value:h.getYear(e.date)}):f.createElement("span",null,I(e.date,h)),f.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},T(e.date,h.options,h))):f.createElement(r.CaptionLabel,{className:y[a.CaptionLabel],role:"status","aria-live":"polite"},T(e.date,h.options,h))),"around"===x&&!t.hideNavigation&&s===b-1&&f.createElement(r.NextMonthButton,{type:"button",className:y[a.NextMonthButton],tabIndex:er?void 0:-1,"aria-disabled":!er||void 0,"aria-label":eb(er),onClick:e_,"data-animated-button":t.animate?"true":void 0},f.createElement(r.Chevron,{disabled:!er||void 0,className:y[a.Chevron],orientation:"rtl"===t.dir?"left":"right"})),s===b-1&&"after"===x&&!t.hideNavigation&&f.createElement(r.Nav,{"data-animated-nav":t.animate?"true":void 0,className:y[a.Nav],style:W?.[a.Nav],"aria-label":eg(),onPreviousClick:eC,onNextClick:e_,previousMonth:et,nextMonth:er}),f.createElement(r.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===g||"range"===g,"aria-label":ey(e.date,h.options,h)||void 0,className:y[a.MonthGrid],style:W?.[a.MonthGrid]},!t.hideWeekdays&&f.createElement(r.Weekdays,{"data-animated-weekdays":t.animate?"true":void 0,className:y[a.Weekdays],style:W?.[a.Weekdays]},S&&f.createElement(r.WeekNumberHeader,{"aria-label":ek(h.options),className:y[a.WeekNumberHeader],style:W?.[a.WeekNumberHeader],scope:"col"},F()),eM.map((e,t)=>f.createElement(r.Weekday,{"aria-label":ew(e,h.options,h),className:y[a.Weekday],key:t,style:W?.[a.Weekday],scope:"col"},P(e,h.options,h)))),f.createElement(r.Weeks,{"data-animated-weeks":t.animate?"true":void 0,className:y[a.Weeks],style:W?.[a.Weeks]},e.weeks.map((e,s)=>f.createElement(r.Week,{className:y[a.Week],key:e.weekNumber,style:W?.[a.Week],week:e},S&&f.createElement(r.WeekNumber,{week:e,style:W?.[a.WeekNumber],"aria-label":eN(e.weekNumber,{locale:p}),className:y[a.WeekNumber],scope:"row",role:"rowheader"},Y(e.weekNumber,h)),e.days.map(e=>{let{date:s}=e,i=en(e);if(i[n.focused]=!i.hidden&&!!ed?.isEqualTo(e),i[o.selected]=eo?.(s)||i.selected,G(ei)){let{from:e,to:t}=ei;i[o.range_start]=!!(e&&t&&h.isSameDay(s,e)),i[o.range_end]=!!(e&&t&&h.isSameDay(s,t)),i[o.range_middle]=z(ei,s,!0,h)}let l=function(e,t={},r={}){let n={...t?.[a.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{n={...n,...r?.[e]}}),n}(i,W,t.modifiersStyles),d=function(e,t,r={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[a])=>(r[a]?e.push(r[a]):t[n[a]]?e.push(t[n[a]]):t[o[a]]&&e.push(t[o[a]]),e),[t[a.Day]])}(i,y,t.modifiersClassNames),c=ej||i.hidden?void 0:ep(s,i,h.options,h);return f.createElement(r.Day,{key:`${h.format(s,"yyyy-MM-dd")}_${h.format(e.displayMonth,"yyyy-MM")}`,day:e,modifiers:i,className:d.join(" "),style:l,role:"gridcell","aria-selected":i.selected||void 0,"aria-label":c,"data-day":h.format(s,"yyyy-MM-dd"),"data-month":e.outside?h.format(s,"yyyy-MM"):void 0,"data-selected":i.selected||void 0,"data-disabled":i.disabled||void 0,"data-hidden":i.hidden||void 0,"data-outside":e.outside||void 0,"data-focused":i.focused||void 0,"data-today":i.today||void 0},!i.hidden&&ej?f.createElement(r.DayButton,{className:y[a.DayButton],style:W?.[a.DayButton],type:"button",day:e,modifiers:i,disabled:i.disabled||void 0,tabIndex:ec(e)?0:-1,"aria-label":eh(s,i,h.options,h),onClick:eE(e,i),onBlur:eS(e,i),onFocus:eO(e,i),onKeyDown:eW(e,i),onMouseEnter:eT(e,i),onMouseLeave:eL(e,i)},L(s,h.options,h)):!i.hidden&&L(e.date,h.options,h))}))))))})),t.footer&&f.createElement(r.Footer,{className:y[a.Footer],style:W?.[a.Footer],role:"status","aria-live":"polite"},t.footer)))}!function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"}(i||(i={}));var tt=r(4780),tr=r(29523);function ta({className:e,classNames:t,showOutsideDays:r=!0,...a}){return(0,u.jsx)(te,{showOutsideDays:r,className:(0,tt.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,tt.cn)((0,tr.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,tt.cn)((0,tr.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({...e})=>(0,u.jsx)(h,{className:"h-4 w-4"}),IconRight:({...e})=>(0,u.jsx)(p,{className:"h-4 w-4"})},...a})}ta.displayName="Calendar";var tn=r(44493),to=r(96834),ts=r(63503),ti=r(89667),tl=r(54300),td=r(34729),tc=r(15079),tu=r(5336);let tf=(0,m.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var tm=r(96474),th=r(40228),tp=r(48730),ty=r(41312),tv=r(29867),tg=r(86401);let tx=()=>{let[e,t]=(0,f.useState)(new Date),[r,a]=(0,f.useState)([]),[n,o]=(0,f.useState)([]),[s,i]=(0,f.useState)(!0),[l,d]=(0,f.useState)(!1),{toast:c}=(0,tv.dj)(),[m,h]=(0,f.useState)({title:"",description:"",patient_id:"",start_time:"",end_time:"",type:"consultation",price:""});(0,f.useEffect)(()=>{p(),y()},[e]);let p=async()=>{try{i(!0);let t=(0,A.GP)(e,"yyyy-MM-dd"),r=await fetch(`/api/appointments?date=${t}`);if(!r.ok)throw Error("Failed to fetch appointments");let n=await r.json();a(Array.isArray(n)?n:[])}catch(e){console.error("Error fetching appointments:",e),a([]),c({title:"Erro ao carregar consultas",description:e instanceof Error?e.message:"Ocorreu um erro inesperado",variant:"destructive"})}finally{i(!1)}},y=async()=>{try{let e=await (0,tg.P)("/api/patients");if(!e.ok)throw Error("Failed to fetch patients");let t=await e.json();console.log("Patients API response:",t);let r=t.data||t;o(Array.isArray(r)?r:[])}catch(e){console.error("Error fetching patients:",e),o([]),c({title:"Erro ao carregar pacientes",description:e instanceof Error?e.message:"Ocorreu um erro inesperado",variant:"destructive"})}},v=async e=>{e.preventDefault();try{let e={...m,status:"scheduled",price:m.price?parseFloat(m.price):null,start_time:new Date(m.start_time).toISOString(),end_time:new Date(m.end_time).toISOString()};if(!(await fetch("/api/appointments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Failed to create appointment");c({title:"Sucesso!",description:"Consulta agendada com sucesso."}),d(!1),h({title:"",description:"",patient_id:"",start_time:"",end_time:"",type:"consultation",price:""}),p()}catch(e){console.error("Error creating appointment:",e),c({title:"Erro ao agendar consulta",description:e instanceof Error?e.message:"Ocorreu um erro inesperado",variant:"destructive"})}},g=async(e,t)=>{try{if(!(await fetch(`/api/appointments/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})})).ok)throw Error("Failed to update appointment");c({title:"Status atualizado",description:"O status da consulta foi atualizado"}),p()}catch(e){console.error("Error updating appointment status:",e),c({title:"Erro ao atualizar consulta",description:e instanceof Error?e.message:"Ocorreu um erro inesperado",variant:"destructive"})}},x=async e=>{try{if(!(await fetch(`/api/appointments/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete appointment");c({title:"Consulta exclu\xedda",description:"A consulta foi removida com sucesso"}),p()}catch(e){console.error("Error deleting appointment:",e),c({title:"Erro ao excluir consulta",description:e instanceof Error?e.message:"Ocorreu um erro inesperado",variant:"destructive"})}},b=e=>{switch(e){case"confirmed":return"bg-success text-success-foreground";case"completed":return"bg-primary text-primary-foreground";case"cancelled":return"bg-destructive text-destructive-foreground";default:return"bg-warning text-warning-foreground"}},w=e=>{switch(e){case"confirmed":case"completed":return(0,u.jsx)(tu.A,{className:"h-4 w-4"});default:return(0,u.jsx)(tf,{className:"h-4 w-4"})}};return s?(0,u.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,u.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):(0,u.jsxs)("div",{className:"space-y-6",children:[(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Agenda"}),(0,u.jsx)("p",{className:"text-muted-foreground",children:"Gerencie suas consultas e hor\xe1rios"})]}),(0,u.jsxs)(ts.lG,{open:l,onOpenChange:d,children:[(0,u.jsx)(ts.zM,{asChild:!0,children:(0,u.jsxs)(tr.$,{children:[(0,u.jsx)(tm.A,{className:"mr-2 h-4 w-4"}),"Nova Consulta"]})}),(0,u.jsx)(ts.Cf,{className:"sm:max-w-[425px]",children:(0,u.jsxs)("form",{onSubmit:v,children:[(0,u.jsxs)(ts.c7,{children:[(0,u.jsx)(ts.L3,{children:"Agendar Nova Consulta"}),(0,u.jsx)(ts.rr,{children:"Preencha os dados para agendar uma nova consulta"})]}),(0,u.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,u.jsxs)("div",{className:"space-y-2",children:[(0,u.jsx)(tl.J,{htmlFor:"title",children:"T\xedtulo"}),(0,u.jsx)(ti.p,{id:"title",value:m.title,onChange:e=>h({...m,title:e.target.value}),required:!0})]}),(0,u.jsxs)("div",{className:"space-y-2",children:[(0,u.jsx)(tl.J,{htmlFor:"patient",children:"Paciente"}),(0,u.jsxs)(tc.l6,{value:m.patient_id,onValueChange:e=>h({...m,patient_id:e}),required:!0,children:[(0,u.jsx)(tc.bq,{children:(0,u.jsx)(tc.yv,{placeholder:"Selecione um paciente"})}),(0,u.jsx)(tc.gC,{children:n.map(e=>(0,u.jsx)(tc.eb,{value:e.id,children:e.name},e.id))})]})]}),(0,u.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,u.jsxs)("div",{className:"space-y-2",children:[(0,u.jsx)(tl.J,{htmlFor:"start_time",children:"In\xedcio"}),(0,u.jsx)(ti.p,{id:"start_time",type:"datetime-local",value:m.start_time,onChange:e=>h({...m,start_time:e.target.value}),required:!0})]}),(0,u.jsxs)("div",{className:"space-y-2",children:[(0,u.jsx)(tl.J,{htmlFor:"end_time",children:"Fim"}),(0,u.jsx)(ti.p,{id:"end_time",type:"datetime-local",value:m.end_time,onChange:e=>h({...m,end_time:e.target.value}),required:!0})]})]}),(0,u.jsxs)("div",{className:"space-y-2",children:[(0,u.jsx)(tl.J,{htmlFor:"type",children:"Tipo"}),(0,u.jsxs)(tc.l6,{value:m.type,onValueChange:e=>h({...m,type:e}),children:[(0,u.jsx)(tc.bq,{children:(0,u.jsx)(tc.yv,{})}),(0,u.jsxs)(tc.gC,{children:[(0,u.jsx)(tc.eb,{value:"consultation",children:"Consulta"}),(0,u.jsx)(tc.eb,{value:"return",children:"Retorno"}),(0,u.jsx)(tc.eb,{value:"teleconsultation",children:"Teleconsulta"})]})]})]}),(0,u.jsxs)("div",{className:"space-y-2",children:[(0,u.jsx)(tl.J,{htmlFor:"price",children:"Valor (R$)"}),(0,u.jsx)(ti.p,{id:"price",type:"number",step:"0.01",value:m.price,onChange:e=>h({...m,price:e.target.value})})]}),(0,u.jsxs)("div",{className:"space-y-2",children:[(0,u.jsx)(tl.J,{htmlFor:"description",children:"Observa\xe7\xf5es"}),(0,u.jsx)(td.T,{id:"description",value:m.description,onChange:e=>h({...m,description:e.target.value})})]})]}),(0,u.jsx)(ts.Es,{children:(0,u.jsx)(tr.$,{type:"submit",children:"Agendar Consulta"})})]})})]})]}),(0,u.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,u.jsxs)(tn.Zp,{children:[(0,u.jsx)(tn.aR,{children:(0,u.jsxs)(tn.ZB,{className:"flex items-center",children:[(0,u.jsx)(th.A,{className:"mr-2 h-5 w-5 text-primary"}),"Calend\xe1rio"]})}),(0,u.jsx)(tn.Wu,{className:"p-3",children:(0,u.jsx)("div",{className:"flex justify-center",children:(0,u.jsx)(ta,{mode:"single",selected:e,onSelect:e=>e&&t(e),className:"rounded-md border-0 shadow-none",classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 hover:bg-accent rounded-md",nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] flex items-center justify-center",row:"flex w-full mt-2",cell:"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50",day:"h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground font-semibold",day_outside:"text-muted-foreground opacity-50",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible"}})})})]}),(0,u.jsx)("div",{className:"lg:col-span-2",children:(0,u.jsxs)(tn.Zp,{children:[(0,u.jsxs)(tn.aR,{children:[(0,u.jsxs)(tn.ZB,{className:"flex items-center",children:[(0,u.jsx)(tp.A,{className:"mr-2 h-5 w-5 text-primary"}),"Consultas - ",(0,A.GP)(e,"dd/MM/yyyy")]}),(0,u.jsxs)(tn.BT,{children:[r.length," consulta(s) agendada(s) para este dia"]})]}),(0,u.jsx)(tn.Wu,{children:0===r.length?(0,u.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,u.jsx)(ty.A,{className:"mx-auto h-12 w-12 mb-4 opacity-50"}),(0,u.jsx)("p",{children:"Nenhuma consulta agendada para este dia"})]}):(0,u.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,u.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-card/50 hover:bg-card transition-colors",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,u.jsx)("div",{className:"flex items-center justify-center w-12 h-12 rounded-full bg-primary/10",children:w(e.status)}),(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"font-medium text-foreground",children:e.title}),(0,u.jsx)("p",{className:"text-sm text-muted-foreground",children:e.patient_name}),(0,u.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,A.GP)(new Date(e.start_time),"HH:mm")," - ",(0,A.GP)(new Date(e.end_time),"HH:mm")]})]})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsxs)(to.E,{className:b(e.status),children:["scheduled"===e.status&&"Agendado","confirmed"===e.status&&"Confirmado","completed"===e.status&&"Conclu\xeddo","cancelled"===e.status&&"Cancelado"]}),"scheduled"===e.status&&(0,u.jsx)(tr.$,{size:"sm",variant:"outline",onClick:()=>g(e.id,"confirmed"),children:"Confirmar"}),"confirmed"===e.status&&(0,u.jsx)(tr.$,{size:"sm",variant:"success",onClick:()=>g(e.id,"completed"),children:"Concluir"}),(0,u.jsx)(tr.$,{size:"sm",variant:"destructive",onClick:()=>x(e.id),children:"Excluir"})]})]},e.id))})})]})})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(60687);r(43210);var n=r(24224),o=r(4780);let s=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,o.cn)(s({variant:t}),e),...r})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[524,822,17,318,704,650,555,542,926],()=>r(34334));module.exports=a})();