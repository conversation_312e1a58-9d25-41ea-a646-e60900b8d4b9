import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Campaign = Tables<'campaigns'>
type CampaignInsert = TablesInsert<'campaigns'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { data: campaigns, error } = await supabase
        .from('campaigns')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(campaigns)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const campaignData: CampaignInsert = {
        ...body,
        user_id: userId,
        status: body.status || 'draft',
        channel: body.channel || 'whatsapp'
      }

      const { data: campaign, error } = await supabase
        .from('campaigns')
        .insert(campaignData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(campaign, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
