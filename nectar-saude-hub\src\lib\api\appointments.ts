import { Appointment, CreateAppointmentDTO, UpdateAppointmentDTO } from '@/types/appointment';

const API_BASE_URL = '/api/appointments';

// Custom error class for API errors
class ApiError extends Error {
  status: number;
  details?: any;

  constructor(message: string, status: number, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.details = details;
  }
}

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  const data = await response.json().catch(() => ({}));
  
  if (!response.ok) {
    console.error('API Error:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      error: data
    });
    
    throw new ApiError(
      data.error || data.message || 'Algo deu errado',
      response.status,
      data.details
    );
  }
  
  return data as T;
}

// Get the auth token
async function getAuthToken(): Promise<string> {
  // Try to get token from localStorage (client-side)
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('supabase.auth.token');
    if (token) return token;
  }
  
  // If using server-side rendering or no token in localStorage
  // You might want to implement token refresh logic here
  throw new Error('No authentication token found');
}

// Create headers with auth token
async function getAuthHeaders() {
  try {
    const token = await getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  } catch (error) {
    console.error('Failed to get auth token:', error);
    throw new ApiError('Authentication required', 401);
  }
}

/**
 * Get appointments with optional date filter
 * @param date Optional date to filter appointments
 * @returns Promise with array of appointments
 */
export async function getAppointments(date?: Date): Promise<Appointment[]> {
  try {
    const params = new URLSearchParams();
    if (date) {
      params.append('date', date.toISOString().split('T')[0]);
    }
    
    const url = params.toString() 
      ? `${API_BASE_URL}?${params}`
      : API_BASE_URL;
    
    const response = await fetch(url, {
      headers: await getAuthHeaders(),
      cache: 'no-store' // Prevent caching for fresh data
    });
    
    return handleResponse<Appointment[]>(response);
  } catch (error) {
    console.error('Failed to fetch appointments:', error);
    throw error;
  }
}

/**
 * Get a single appointment by ID
 * @param id Appointment ID
 * @returns Promise with the appointment
 */
export async function getAppointment(id: string): Promise<Appointment> {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      headers: await getAuthHeaders(),
      cache: 'no-store'
    });
    
    return handleResponse<Appointment>(response);
  } catch (error) {
    console.error(`Failed to fetch appointment ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new appointment
 * @param appointmentData Appointment data
 * @returns Promise with the created appointment
 */
export async function createAppointment(appointmentData: CreateAppointmentDTO): Promise<Appointment> {
  try {
    const response = await fetch(API_BASE_URL, {
      method: 'POST',
      headers: await getAuthHeaders(),
      body: JSON.stringify(appointmentData),
    });
    
    return handleResponse<Appointment>(response);
  } catch (error) {
    console.error('Failed to create appointment:', error);
    throw error;
  }
}

/**
 * Update an existing appointment
 * @param id Appointment ID
 * @param updateData Data to update
 * @returns Promise with the updated appointment
 */
export async function updateAppointment(
  id: string, 
  updateData: UpdateAppointmentDTO
): Promise<Appointment> {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'PUT',
      headers: await getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    
    return handleResponse<Appointment>(response);
  } catch (error) {
    console.error(`Failed to update appointment ${id}:`, error);
    throw error;
  }
}

/**
 * Delete an appointment
 * @param id Appointment ID to delete
 */
export async function deleteAppointment(id: string): Promise<void> {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'DELETE',
      headers: await getAuthHeaders(),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.error || 'Failed to delete appointment',
        response.status,
        errorData.details
      );
    }
  } catch (error) {
    console.error(`Failed to delete appointment ${id}:`, error);
    throw error;
  }
}

export { ApiError };
