(()=>{var e={};e.id=492,e.ids=[492],e.modules={641:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>N});var s=r(687),o=r(9867),a=r(3210),n=r(7313),i=r(4224),d=r(1860),l=r(4780);let u=n.Kq,c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.LM,{ref:r,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=n.LM.displayName;let p=(0,i.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),m=a.forwardRef(({className:e,variant:t,...r},o)=>(0,s.jsx)(n.bL,{ref:o,className:(0,l.cn)(p({variant:t}),e),...r}));m.displayName=n.bL.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.rc,{ref:r,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.rc.displayName;let f=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.bm,{ref:r,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}));f.displayName=n.bm.displayName;let v=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.hE,{ref:r,className:(0,l.cn)("text-sm font-semibold",e),...t}));v.displayName=n.hE.displayName;let x=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.VY,{ref:r,className:(0,l.cn)("text-sm opacity-90",e),...t}));function h(){let{toasts:e}=(0,o.dj)();return(0,s.jsxs)(u,{children:[e.map(function({id:e,title:t,description:r,action:o,...a}){return(0,s.jsxs)(m,{...a,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&(0,s.jsx)(v,{children:t}),r&&(0,s.jsx)(x,{children:r})]}),o,(0,s.jsx)(f,{})]},e)}),(0,s.jsx)(c,{})]})}x.displayName=n.VY.displayName;var g=r(218),b=r(2581);let P=({...e})=>{let{theme:t="system"}=(0,g.D)();return(0,s.jsx)(b.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var y=r(6242),j=r(2314),w=r(8693);function _({children:e}){let[t]=(0,a.useState)(()=>new j.E({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,s.jsx)(w.Ht,{client:t,children:e})}function N({children:e}){return(0,s.jsx)(_,{children:(0,s.jsxs)(y.Bc,{children:[e,(0,s.jsx)(h,{}),(0,s.jsx)(P,{})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1045:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(5239),o=r(8088),a=r(8170),n=r.n(a),i=r(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1135:()=>{},1475:(e,t,r)=>{Promise.resolve().then(r.bind(r,3622))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3622:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\Providers.tsx","Providers")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>i});var s=r(7413),o=r(5091),a=r.n(o);r(1135);var n=r(3622);let i={title:"Nectar Sa\xfade - Plataforma para Profissionais da Sa\xfade",description:"Plataforma completa para profissionais da sa\xfade gerenciarem consultas, pacientes e relacionamento via WhatsApp em um s\xf3 lugar."};function d({children:e}){return(0,s.jsx)("html",{lang:"pt-BR",children:(0,s.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:(0,s.jsx)(n.Providers,{children:e})})})}},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(9384),o=r(2348);function a(...e){return(0,o.QP)((0,s.$)(e))}},5547:(e,t,r)=>{Promise.resolve().then(r.bind(r,641))},6242:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>i,ZI:()=>u,k$:()=>l,m_:()=>d});var s=r(687),o=r(3210),a=r(592),n=r(4780);let i=a.Kq,d=a.bL,l=a.l9,u=o.forwardRef(({className:e,sideOffset:t=4,...r},o)=>(0,s.jsx)(a.UC,{ref:o,sideOffset:t,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));u.displayName=a.UC.displayName},6899:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},7579:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9867:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p});var s=r(3210);let o=0,a=new Map,n=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function u(e){l=i(l,e),d.forEach(e=>{e(l)})}function c({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=s.useState(l);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,437],()=>r(1045));module.exports=s})();