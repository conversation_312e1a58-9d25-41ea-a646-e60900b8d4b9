/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/stats/route.ts */ \"(rsc)/./src/app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/dashboard/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./src/lib/api-utils.ts\");\n\nasync function GET(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            // Get today's date range\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            const tomorrow = new Date(today);\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            // Fetch today's appointments count\n            const { count: todayCount } = await supabase.from('appointments').select('*', {\n                count: 'exact',\n                head: true\n            }).eq('user_id', userId).gte('start_time', today.toISOString()).lt('start_time', tomorrow.toISOString());\n            // Fetch total patients count\n            const { count: patientsCount } = await supabase.from('patients').select('*', {\n                count: 'exact',\n                head: true\n            }).eq('user_id', userId);\n            // Fetch recent appointments with patient info\n            const { data: appointments } = await supabase.from('appointments').select(`\n          *,\n          patients!inner(name)\n        `).eq('user_id', userId).gte('start_time', today.toISOString()).order('start_time').limit(5);\n            const stats = {\n                todayAppointments: todayCount || 0,\n                totalPatients: patientsCount || 0,\n                unreadMessages: 5,\n                monthlyRevenue: 8250 // Mock data for now\n            };\n            const recentAppointments = appointments?.map((apt)=>({\n                    time: new Date(apt.start_time).toLocaleTimeString('pt-BR', {\n                        hour: '2-digit',\n                        minute: '2-digit'\n                    }),\n                    patient: apt.patients.name,\n                    type: apt.type === 'consultation' ? 'Consulta' : apt.type === 'return' ? 'Retorno' : 'Teleconsulta',\n                    status: apt.status\n                })) || [];\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)({\n                stats,\n                recentAppointments\n            });\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-utils.ts":
/*!******************************!*\
  !*** ./src/lib/api-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nfunction createApiResponse(data, error, status = 200) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        data,\n        error\n    }, {\n        status\n    });\n}\nasync function withAuth(request, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return createApiResponse(undefined, 'Unauthorized', 401);\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return createApiResponse(undefined, error instanceof Error ? error.message : 'Internal server error', 500);\n    }\n}\nfunction handleApiError(error) {\n    console.error('API Error:', error);\n    if (error?.code === 'PGRST116') {\n        return createApiResponse(undefined, 'Resource not found', 404);\n    }\n    if (error?.code === '23505') {\n        return createApiResponse(undefined, 'Resource already exists', 409);\n    }\n    return createApiResponse(undefined, error?.message || 'Internal server error', 500);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zmwdnemlzndjavlriyrc.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();