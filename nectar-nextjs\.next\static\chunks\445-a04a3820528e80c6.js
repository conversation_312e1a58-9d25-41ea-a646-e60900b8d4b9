"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[445],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1497:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2432:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},2713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3062:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("megaphone",[["path",{d:"M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z",key:"q8bfy3"}],["path",{d:"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14",key:"1853fq"}],["path",{d:"M8 6v8",key:"15ugcq"}]])},3109:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3651:(e,t,n)=>{n.d(t,{bm:()=>e6,UC:()=>e8,VY:()=>e9,hJ:()=>e7,ZL:()=>e4,bL:()=>e2,hE:()=>e5,l9:()=>e3});var r,o,a=n(2115),i=n(5185),c=n(6101),l=n(6081),u=n(1285),d=n(5845),s=n(9178),f=n(3655),p=n(9033),v=n(5155),h="focusScope.autoFocusOnMount",m="focusScope.autoFocusOnUnmount",y={bubbles:!1,cancelable:!0},g=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,d]=a.useState(null),s=(0,p.c)(o),g=(0,p.c)(i),x=a.useRef(null),A=(0,c.s)(t,e=>d(e)),C=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(C.paused||!u)return;let t=e.target;u.contains(t)?x.current=t:w(x.current,{select:!0})},t=function(e){if(C.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||w(x.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&w(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,C.paused]),a.useEffect(()=>{if(u){k.add(C);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(h,y);u.addEventListener(h,s),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(w(r,{select:t}),document.activeElement!==n)return}(b(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&w(u))}return()=>{u.removeEventListener(h,s),setTimeout(()=>{let t=new CustomEvent(m,y);u.addEventListener(m,g),u.dispatchEvent(t),t.defaultPrevented||w(null!=e?e:document.body,{select:!0}),u.removeEventListener(m,g),k.remove(C)},0)}}},[u,s,g,C]);let M=a.useCallback(e=>{if(!n&&!r||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=b(e);return[E(t,e),E(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&w(a,{select:!0})):(e.preventDefault(),n&&w(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,C.paused]);return(0,v.jsx)(f.sG.div,{tabIndex:-1,...l,ref:A,onKeyDown:M})});function b(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function E(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function w(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}g.displayName="FocusScope";var k=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=x(e,t)).unshift(t)},remove(t){var n;null==(n=(e=x(e,t))[0])||n.resume()}}}();function x(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var A=n(4378),C=n(8905),M=0;function R(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var S=function(){return(S=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function N(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var D=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),j="width-before-scroll-bar";function O(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var T="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,I=new WeakMap;function L(e){return e}var P=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=L),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return a.options=S({async:!0,ssr:!1},e),a}(),F=function(){},_=a.forwardRef(function(e,t){var n,r,o,i,c=a.useRef(null),l=a.useState({onScrollCapture:F,onWheelCapture:F,onTouchMoveCapture:F}),u=l[0],d=l[1],s=e.forwardProps,f=e.children,p=e.className,v=e.removeScrollBar,h=e.enabled,m=e.shards,y=e.sideCar,g=e.noRelative,b=e.noIsolation,E=e.inert,w=e.allowPinchZoom,k=e.as,x=e.gapMode,A=N(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(n=[c,t],r=function(e){return n.forEach(function(t){return O(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,T(function(){var e=I.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||O(e,null)}),r.forEach(function(e){t.has(e)||O(e,o)})}I.set(i,n)},[n]),i),M=S(S({},A),u);return a.createElement(a.Fragment,null,h&&a.createElement(y,{sideCar:P,removeScrollBar:v,shards:m,noRelative:g,noIsolation:b,inert:E,setCallbacks:d,allowPinchZoom:!!w,lockRef:c,gapMode:x}),s?a.cloneElement(a.Children.only(f),S(S({},M),{ref:C})):a.createElement(void 0===k?"div":k,S({},M,{className:p,ref:C}),f))});_.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},_.classNames={fullWidth:j,zeroRight:D};var W=function(e){var t=e.sideCar,n=N(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,S({},n))};W.isSideCarExport=!0;var q=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},B=function(){var e=q();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},z=function(){var e=B();return function(t){return e(t.styles,t.dynamic),null}},K={left:0,top:0,right:0,gap:0},V=function(e){return parseInt(e||"",10)||0},G=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[V(n),V(r),V(o)]},H=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return K;var t=G(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Y=z(),X="data-scroll-locked",Z=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(X,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(D," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(j," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(D," .").concat(D," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(j," .").concat(j," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(X,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},U=function(){var e=parseInt(document.body.getAttribute(X)||"0",10);return isFinite(e)?e:0},J=function(){a.useEffect(function(){return document.body.setAttribute(X,(U()+1).toString()),function(){var e=U()-1;e<=0?document.body.removeAttribute(X):document.body.setAttribute(X,e.toString())}},[])},Q=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;J();var i=a.useMemo(function(){return H(o)},[o]);return a.createElement(Y,{styles:Z(i,!t,o,n?"":"!important")})},$=!1;if("undefined"!=typeof window)try{var ee=Object.defineProperty({},"passive",{get:function(){return $=!0,!0}});window.addEventListener("test",ee,ee),window.removeEventListener("test",ee,ee)}catch(e){$=!1}var et=!!$&&{passive:!1},en=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},er=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),eo(e,r)){var o=ea(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},eo=function(e,t){return"v"===e?en(t,"overflowY"):en(t,"overflowX")},ea=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ei=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,l=n.target,u=t.contains(l),d=!1,s=c>0,f=0,p=0;do{if(!l)break;var v=ea(e,l),h=v[0],m=v[1]-v[2]-i*h;(h||m)&&eo(e,l)&&(f+=m,p+=h);var y=l.parentNode;l=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return s&&(o&&1>Math.abs(f)||!o&&c>f)?d=!0:!s&&(o&&1>Math.abs(p)||!o&&-c>p)&&(d=!0),d},ec=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},el=function(e){return[e.deltaX,e.deltaY]},eu=function(e){return e&&"current"in e?e.current:e},ed=0,es=[];let ef=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(ed++)[0],i=a.useState(z)[0],c=a.useRef(e);a.useEffect(function(){c.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eu),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,a=ec(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-a[0],u="deltaY"in e?e.deltaY:i[1]-a[1],d=e.target,s=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===s&&"range"===d.type)return!1;var f=er(s,d);if(!f)return!0;if(f?o=s:(o="v"===s?"h":"v",f=er(s,d)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=o),!o)return!0;var p=r.current||o;return ei(p,t,e,"h"===p?l:u,!0)},[]),u=a.useCallback(function(e){if(es.length&&es[es.length-1]===i){var n="deltaY"in e?el(e):ec(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(eu).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),s=a.useCallback(function(e){n.current=ec(e),r.current=void 0},[]),f=a.useCallback(function(t){d(t.type,el(t),t.target,l(t,e.lockRef.current))},[]),p=a.useCallback(function(t){d(t.type,ec(t),t.target,l(t,e.lockRef.current))},[]);a.useEffect(function(){return es.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,et),document.addEventListener("touchmove",u,et),document.addEventListener("touchstart",s,et),function(){es=es.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,et),document.removeEventListener("touchmove",u,et),document.removeEventListener("touchstart",s,et)}},[]);var v=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?a.createElement(Q,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},P.useMedium(r),W);var ep=a.forwardRef(function(e,t){return a.createElement(_,S({},e,{ref:t,sideCar:ef}))});ep.classNames=_.classNames;var ev=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eh=new WeakMap,em=new WeakMap,ey={},eg=0,eb=function(e){return e&&(e.host||eb(e.parentNode))},eE=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eb(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});ey[n]||(ey[n]=new WeakMap);var a=ey[n],i=[],c=new Set,l=new Set(o),u=function(e){!e||c.has(e)||(c.add(e),u(e.parentNode))};o.forEach(u);var d=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(c.has(e))d(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(eh.get(e)||0)+1,u=(a.get(e)||0)+1;eh.set(e,l),a.set(e,u),i.push(e),1===l&&o&&em.set(e,!0),1===u&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),c.clear(),eg++,function(){i.forEach(function(e){var t=eh.get(e)-1,o=a.get(e)-1;eh.set(e,t),a.set(e,o),t||(em.has(e)||e.removeAttribute(r),em.delete(e)),o||e.removeAttribute(n)}),--eg||(eh=new WeakMap,eh=new WeakMap,em=new WeakMap,ey={})}},ew=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ev(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),eE(r,o,n,"aria-hidden")):function(){return null}},ek=n(9708),ex="Dialog",[eA,eC]=(0,l.A)(ex),[eM,eR]=eA(ex),eS=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:c=!0}=e,l=a.useRef(null),s=a.useRef(null),[f,p]=(0,d.i)({prop:r,defaultProp:null!=o&&o,onChange:i,caller:ex});return(0,v.jsx)(eM,{scope:t,triggerRef:l,contentRef:s,contentId:(0,u.B)(),titleId:(0,u.B)(),descriptionId:(0,u.B)(),open:f,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};eS.displayName=ex;var eN="DialogTrigger",eD=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eR(eN,n),a=(0,c.s)(t,o.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eU(o.open),...r,ref:a,onClick:(0,i.m)(e.onClick,o.onOpenToggle)})});eD.displayName=eN;var ej="DialogPortal",[eO,eT]=eA(ej,{forceMount:void 0}),eI=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=eR(ej,t);return(0,v.jsx)(eO,{scope:t,forceMount:n,children:a.Children.map(r,e=>(0,v.jsx)(C.C,{present:n||i.open,children:(0,v.jsx)(A.Z,{asChild:!0,container:o,children:e})}))})};eI.displayName=ej;var eL="DialogOverlay",eP=a.forwardRef((e,t)=>{let n=eT(eL,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eR(eL,e.__scopeDialog);return a.modal?(0,v.jsx)(C.C,{present:r||a.open,children:(0,v.jsx)(e_,{...o,ref:t})}):null});eP.displayName=eL;var eF=(0,ek.TL)("DialogOverlay.RemoveScroll"),e_=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eR(eL,n);return(0,v.jsx)(ep,{as:eF,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":eU(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eW="DialogContent",eq=a.forwardRef((e,t)=>{let n=eT(eW,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eR(eW,e.__scopeDialog);return(0,v.jsx)(C.C,{present:r||a.open,children:a.modal?(0,v.jsx)(eB,{...o,ref:t}):(0,v.jsx)(ez,{...o,ref:t})})});eq.displayName=eW;var eB=a.forwardRef((e,t)=>{let n=eR(eW,e.__scopeDialog),r=a.useRef(null),o=(0,c.s)(t,n.contentRef,r);return a.useEffect(()=>{let e=r.current;if(e)return ew(e)},[]),(0,v.jsx)(eK,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),ez=a.forwardRef((e,t)=>{let n=eR(eW,e.__scopeDialog),r=a.useRef(!1),o=a.useRef(!1);return(0,v.jsx)(eK,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(r.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let c=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(c))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eK=a.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=eR(eW,n),d=a.useRef(null),f=(0,c.s)(t,d);return a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:R()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:R()),M++,()=>{1===M&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),M--}},[]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(g,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,v.jsx)(s.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":eU(u.open),...l,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(e0,{titleId:u.titleId}),(0,v.jsx)(e1,{contentRef:d,descriptionId:u.descriptionId})]})]})}),eV="DialogTitle",eG=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eR(eV,n);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});eG.displayName=eV;var eH="DialogDescription",eY=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eR(eH,n);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});eY.displayName=eH;var eX="DialogClose",eZ=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eR(eX,n);return(0,v.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})});function eU(e){return e?"open":"closed"}eZ.displayName=eX;var eJ="DialogTitleWarning",[eQ,e$]=(0,l.q)(eJ,{contentName:eW,titleName:eV,docsSlug:"dialog"}),e0=e=>{let{titleId:t}=e,n=e$(eJ),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return a.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},e1=e=>{let{contentRef:t,descriptionId:n}=e,r=e$("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return a.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},e2=eS,e3=eD,e4=eI,e7=eP,e8=eq,e5=eG,e9=eY,e6=eZ},3783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},4186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7489:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(2115),o=n(3655),a=n(5155),i="horizontal",c=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:l=i,...u}=e,d=(n=l,c.includes(n))?l:i;return(0,a.jsx)(o.sG.div,{"data-orientation":d,...r?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:t})});l.displayName="Separator";var u=l},7580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9881:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])}}]);