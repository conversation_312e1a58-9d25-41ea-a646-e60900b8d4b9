"use client"

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Calendar,
  Users, 
  MessageSquare, 
  BarChart3, 
  Shield, 
  Smartphone,
  Clock,
  Heart,
  CheckCircle,
  ArrowRight
} from "lucide-react";

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-primary mr-2" />
              <span className="text-xl font-bold text-foreground">Nectar Saúde</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="text-muted-foreground hover:text-foreground transition-colors">Funcionalidades</a>
              <a href="#pricing" className="text-muted-foreground hover:text-foreground transition-colors">Preços</a>
              <a href="#contact" className="text-muted-foreground hover:text-foreground transition-colors">Contato</a>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth">
                <Button variant="ghost">Entrar</Button>
              </Link>
              <Link href="/auth">
                <Button>Começar Grátis</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  Sua prática médica
                  <span className="block text-primary">conectada e organizada</span>
                </h1>
                <p className="text-xl text-muted-foreground max-w-2xl">
                  Plataforma completa para profissionais da saúde gerenciarem consultas, 
                  pacientes e relacionamento via WhatsApp em um só lugar.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/auth">
                  <Button size="lg" className="text-lg px-8 py-4">
                    Começar Agora
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Button size="lg" variant="outline" className="text-lg px-8 py-4">
                  Ver Demo
                </Button>
              </div>

              <div className="grid grid-cols-3 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">500+</div>
                  <div className="text-muted-foreground text-sm">Profissionais</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">50k+</div>
                  <div className="text-muted-foreground text-sm">Consultas</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">98%</div>
                  <div className="text-muted-foreground text-sm">Satisfação</div>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="w-full h-96 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl flex items-center justify-center">
                <Heart className="h-32 w-32 text-primary/30" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Tudo que você precisa em uma plataforma
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Simplifique sua rotina com ferramentas integradas para agendamento, 
              atendimento e relacionamento com pacientes.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Smartphone,
                title: "WhatsApp Integrado",
                description: "Receba e gerencie todas as mensagens dos pacientes diretamente na plataforma."
              },
              {
                icon: Calendar,
                title: "Agenda Inteligente",
                description: "Calendario configurável com disponibilidade automática e lembretes."
              },
              {
                icon: Users,
                title: "CRM Completo",
                description: "Histórico completo de conversas, anotações e campanhas de marketing."
              },
              {
                icon: BarChart3,
                title: "Dashboard Analítico",
                description: "Visualize receitas, conversões e performance em tempo real."
              },
              {
                icon: MessageSquare,
                title: "Multi-canal",
                description: "WhatsApp, Instagram e e-mail centralizados em um só lugar."
              },
              {
                icon: Shield,
                title: "Segurança LGPD",
                description: "Dados criptografados e compliance total com regulamentações."
              }
            ].map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardHeader>
                  <feature.icon className="h-12 w-12 text-primary mb-4" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h3 className="text-3xl lg:text-4xl font-bold text-foreground">
                  Economize tempo e aumente sua receita
                </h3>
                <p className="text-lg text-muted-foreground">
                  Profissionais que usam o Nectar Saúde relatam aumento de 40% na eficiência 
                  e 25% no faturamento médio mensal.
                </p>
              </div>

              <div className="space-y-4">
                {[
                  "Agendamento automatizado 24/7",
                  "Redução de 80% em no-shows",
                  "Campanhas de reativação eficazes",
                  "Relatórios financeiros detalhados"
                ].map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-500 flex-shrink-0" />
                    <span className="text-foreground text-lg">{benefit}</span>
                  </div>
                ))}
              </div>

              <Link href="/auth">
                <Button size="lg" className="text-lg px-8 py-4">
                  Experimentar Grátis por 14 dias
                </Button>
              </Link>
            </div>

            <div className="space-y-6">
              <Card className="p-6 border-l-4 border-l-primary">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary/10 p-3 rounded-full">
                    <Clock className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">Economia de Tempo</h4>
                    <p className="text-muted-foreground">
                      "Recuperei 3 horas por dia que gastava organizando agenda e mensagens."
                    </p>
                    <p className="text-sm text-primary font-medium mt-2">- Dra. Maria Silva, Psicóloga</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 border-l-4 border-l-green-500">
                <div className="flex items-start space-x-4">
                  <div className="bg-green-500/10 p-3 rounded-full">
                    <BarChart3 className="h-6 w-6 text-green-500" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">Aumento de Receita</h4>
                    <p className="text-muted-foreground">
                      "Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas."
                    </p>
                    <p className="text-sm text-green-500 font-medium mt-2">- Dr. João Santos, Médico</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h3 className="text-3xl lg:text-4xl font-bold text-white">
              Pronto para transformar sua prática médica?
            </h3>
            <p className="text-xl text-white/90">
              Junte-se a centenas de profissionais que já revolucionaram 
              seu atendimento com o Nectar Saúde.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <Button size="lg" variant="outline" className="text-lg px-8 py-4">
                  Começar Teste Grátis
                </Button>
              </Link>
              <Button size="lg" variant="secondary" className="text-lg px-8 py-4">
                Falar com Especialista
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-card border-t py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <Heart className="h-8 w-8 text-primary mr-2" />
                <span className="text-xl font-bold text-foreground">Nectar Saúde</span>
              </div>
              <p className="text-muted-foreground mb-4">
                Conectando profissionais da saúde com seus pacientes através de tecnologia intuitiva e segura.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-foreground mb-4">Produto</h4>
              <div className="space-y-2">
                <a href="#" className="block text-muted-foreground hover:text-foreground">Funcionalidades</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground">Preços</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground">Integrações</a>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-foreground mb-4">Suporte</h4>
              <div className="space-y-2">
                <a href="#" className="block text-muted-foreground hover:text-foreground">Central de Ajuda</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground">Contato</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground">Status</a>
              </div>
            </div>
          </div>
          
          <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-muted-foreground">&copy; 2024 Nectar Saúde. Todos os direitos reservados.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-muted-foreground hover:text-foreground">Privacidade</a>
              <a href="#" className="text-muted-foreground hover:text-foreground">Termos</a>
              <a href="#" className="text-muted-foreground hover:text-foreground">LGPD</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
