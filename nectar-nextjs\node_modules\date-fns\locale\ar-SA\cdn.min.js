(()=>{var $;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},U(B)}function A(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),G.push.apply(G,H)}return G}function Q(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?A(Object(G),!0).forEach(function(H){E(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):A(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function E(B,C,G){if(C=N(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function N(B){var C=z(B,"string");return U(C)=="symbol"?C:String(C)}function z(B,C){if(U(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var W=Object.defineProperty,GB=function B(C,G){for(var H in G)W(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function J(X){return G[H]=function(){return X}}})},D={lessThanXSeconds:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0648\u0627\u0646\u064A",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0627\u0646\u064A\u0629"},xSeconds:{one:"\u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"{{count}} \u062B\u0648\u0627\u0646\u064A",other:"{{count}} \u062B\u0627\u0646\u064A\u0629"},halfAMinute:"\u0646\u0635\u0641 \u062F\u0642\u064A\u0642\u0629",lessThanXMinutes:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u0627\u0626\u0642",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u064A\u0642\u0629"},xMinutes:{one:"\u062F\u0642\u064A\u0642\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"{{count}} \u062F\u0642\u0627\u0626\u0642",other:"{{count}} \u062F\u0642\u064A\u0642\u0629"},aboutXHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0633\u0627\u0639\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0633\u0627\u0639\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0633\u0627\u0639\u062A\u064A\u0646",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A",other:"{{count}} \u0633\u0627\u0639\u0629"},xDays:{one:"\u064A\u0648\u0645 \u0648\u0627\u062D\u062F",two:"\u064A\u0648\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u064A\u0627\u0645",other:"{{count}} \u064A\u0648\u0645"},aboutXWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0623\u0633\u0628\u0648\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639",other:"{{count}} \u0623\u0633\u0628\u0648\u0639"},aboutXMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0634\u0647\u0631\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F",two:"\u0634\u0647\u0631\u064A\u0646",threeToTen:"{{count}} \u0623\u0634\u0647\u0631",other:"{{count}} \u0634\u0647\u0631"},aboutXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F",two:"\u0639\u0627\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645",other:"{{count}} \u0639\u0627\u0645"},overXYears:{one:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645",two:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645\u064A\u0646",threeToTen:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0623\u0639\u0648\u0627\u0645",other:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0639\u0627\u0645"},almostXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"}},M=function B(C,G,H){var J,X=D[C];if(typeof X==="string")J=X;else if(G===1)J=X.one;else if(G===2)J=X.two;else if(G<=10)J=X.threeToTen.replace("{{count}}",String(G));else J=X.other.replace("{{count}}",String(G));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"\u0641\u064A \u062E\u0644\u0627\u0644 "+J;else return"\u0645\u0646\u0630 "+J;return J};function K(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var S={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} '\u0639\u0646\u062F' {{time}}",long:"{{date}} '\u0639\u0646\u062F' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:K({formats:S,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u0623\u062E\u0631' eeee '\u0639\u0646\u062F' p",yesterday:"'\u0623\u0645\u0633 \u0639\u0646\u062F' p",today:"'\u0627\u0644\u064A\u0648\u0645 \u0639\u0646\u062F' p",tomorrow:"'\u063A\u062F\u0627\u064B \u0639\u0646\u062F' p",nextWeek:"eeee '\u0639\u0646\u062F' p",other:"P"},w=function B(C,G,H,J){return j[C]};function I(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(C):C;return J[T]}}var _={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645.","\u0628.\u0645."],wide:["\u0642\u0628\u0644 \u0627\u0644\u0645\u064A\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0644\u0645\u064A\u0644\u0627\u062F"]},f={narrow:["1","2","3","4"],abbreviated:["\u06311","\u06312","\u06313","\u06314"],wide:["\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0623\u0648\u0644","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0646\u064A","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0644\u062B","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0631\u0627\u0628\u0639"]},F={narrow:["\u064A","\u0641","\u0645","\u0623","\u0645","\u064A","\u064A","\u0623","\u0633","\u0623","\u0646","\u062F"],abbreviated:["\u064A\u0646\u0627","\u0641\u0628\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A\u0648","\u064A\u0648\u0646\u0640","\u064A\u0648\u0644\u0640","\u0623\u063A\u0633\u0640","\u0633\u0628\u062A\u0640","\u0623\u0643\u062A\u0640","\u0646\u0648\u0641\u0640","\u062F\u064A\u0633\u0640"],wide:["\u064A\u0646\u0627\u064A\u0631","\u0641\u0628\u0631\u0627\u064A\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A\u0648","\u064A\u0648\u0646\u064A\u0648","\u064A\u0648\u0644\u064A\u0648","\u0623\u063A\u0633\u0637\u0633","\u0633\u0628\u062A\u0645\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0641\u0645\u0628\u0631","\u062F\u064A\u0633\u0645\u0628\u0631"]},v={narrow:["\u062D","\u0646","\u062B","\u0631","\u062E","\u062C","\u0633"],short:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],abbreviated:["\u0623\u062D\u062F","\u0627\u062B\u0646\u0640","\u062B\u0644\u0627","\u0623\u0631\u0628\u0640","\u062E\u0645\u064A\u0640","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],wide:["\u0627\u0644\u0623\u062D\u062F","\u0627\u0644\u0627\u062B\u0646\u064A\u0646","\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621","\u0627\u0644\u062E\u0645\u064A\u0633","\u0627\u0644\u062C\u0645\u0639\u0629","\u0627\u0644\u0633\u0628\u062A"]},P={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"}},k={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"}},b=function B(C){return String(C)},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=C.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?y(Z,function(x){return x.test(Y)}):m(Z,function(x){return x.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=G.valueCallback?G.valueCallback(T):T;var CB=C.slice(Y.length);return{value:T,rest:CB}}}function m(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function y(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function c(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var J=H[0],X=C.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(J.length);return{value:Y,rest:Z}}}var p=/^(\d+)(th|st|nd|rd)?/i,d=/\d+/i,g={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?م\.?|ق\.?\s?م\.?\s?|a\.?\s?d\.?|c\.?\s?)/i,wide:/^(قبل الميلاد|قبل الميلاد|بعد الميلاد|بعد الميلاد)/i},u={any:[/^قبل/i,/^بعد/i]},l={narrow:/^[1234]/i,abbreviated:/^ر[1234]/i,wide:/^الربع [1234]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[يفمأمسند]/i,abbreviated:/^(ين|ف|مار|أب|ماي|يون|يول|أغ|س|أك|ن|د)/i,wide:/^(ين|ف|مار|أب|ماي|يون|يول|أغ|س|أك|ن|د)/i},s={narrow:[/^ي/i,/^ف/i,/^م/i,/^أ/i,/^م/i,/^ي/i,/^ي/i,/^أ/i,/^س/i,/^أ/i,/^ن/i,/^د/i],any:[/^ين/i,/^ف/i,/^مار/i,/^أب/i,/^ماي/i,/^يون/i,/^يول/i,/^أغ/i,/^س/i,/^أك/i,/^ن/i,/^د/i]},o={narrow:/^[حنثرخجس]/i,short:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,abbreviated:/^(أحد|اثن|ثلا|أرب|خمي|جمعة|سبت)/i,wide:/^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i},r={narrow:[/^ح/i,/^ن/i,/^ث/i,/^ر/i,/^خ/i,/^ج/i,/^س/i],wide:[/^الأحد/i,/^الاثنين/i,/^الثلاثاء/i,/^الأربعاء/i,/^الخميس/i,/^الجمعة/i,/^السبت/i],any:[/^أح/i,/^اث/i,/^ث/i,/^أر/i,/^خ/i,/^ج/i,/^س/i]},a={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},t={ordinalNumber:c({matchPattern:p,parsePattern:d,valueCallback:function B(C){return parseInt(C,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"ar-SA",formatDistance:M,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{arSA:BB})})})();

//# debugId=CA7454397497F65964756E2164756E21
