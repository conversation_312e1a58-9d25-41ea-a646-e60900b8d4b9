// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

// Create and export the Supabase client
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  }
});

// Export types for better type safety
export type { Database } from './types';

// Helper function to handle API errors
const handleApiError = (error: unknown) => {
  console.error('Supabase API Error:', error);
  throw error;
};

// Export a typed API client
export const api = {
  // Appointments
  appointments: {
    async getAll(date?: Date) {
      let query = supabase.from('appointments').select('*');
      
      if (date) {
        const startDate = new Date(date);
        startDate.setHours(0, 0, 0, 0);
        
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 1);
        
        query = query
          .gte('start_time', startDate.toISOString())
          .lt('start_time', endDate.toISOString());
      }
      
      const { data, error } = await query.order('start_time');
      if (error) handleApiError(error);
      return data || [];
    },
    
    async getById(id: string) {
      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) handleApiError(error);
      return data;
    },
    
    async create(appointment: any) {
      const { data, error } = await supabase
        .from('appointments')
        .insert(appointment)
        .select()
        .single();
      
      if (error) handleApiError(error);
      return data;
    },
    
    async update(id: string, updates: any) {
      const { data, error } = await supabase
        .from('appointments')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) handleApiError(error);
      return data;
    },
    
    async delete(id: string) {
      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', id);
      
      if (error) handleApiError(error);
      return true;
    },
  },
  
  // Patients
  patients: {
    async getAll() {
      const { data, error } = await supabase
        .from('patients')
        .select('*')
        .order('name');
      
      if (error) handleApiError(error);
      return data || [];
    },
    
    async getById(id: string) {
      const { data, error } = await supabase
        .from('patients')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) handleApiError(error);
      return data;
    },
  },
};